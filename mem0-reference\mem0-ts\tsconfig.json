{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2018", "module": "ESNext", "lib": ["dom", "ES2021", "dom.iterable"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "composite": false, "types": ["@types/node"], "jsx": "react-jsx", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "inlineSources": false, "isolatedModules": true, "stripInternal": true, "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}