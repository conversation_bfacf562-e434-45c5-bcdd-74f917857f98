lockfileVersion: "9.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      "@anthropic-ai/sdk":
        specifier: ^0.40.1
        version: 0.40.1(encoding@0.1.13)
      "@google/genai":
        specifier: ^1.2.0
        version: 1.2.0(@modelcontextprotocol/sdk@1.12.1)(encoding@0.1.13)
      "@langchain/core":
        specifier: ^0.3.44
        version: 0.3.44(openai@4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2))
      "@mistralai/mistralai":
        specifier: ^1.5.2
        version: 1.5.2(zod@3.24.2)
      "@qdrant/js-client-rest":
        specifier: 1.13.0
        version: 1.13.0(typescript@5.5.4)
      "@supabase/supabase-js":
        specifier: ^2.49.1
        version: 2.49.1
      "@types/jest":
        specifier: 29.5.14
        version: 29.5.14
      "@types/pg":
        specifier: 8.11.0
        version: 8.11.0
      "@types/sqlite3":
        specifier: 3.1.11
        version: 3.1.11
      axios:
        specifier: 1.7.7
        version: 1.7.7
      groq-sdk:
        specifier: 0.3.0
        version: 0.3.0(encoding@0.1.13)
      neo4j-driver:
        specifier: ^5.28.1
        version: 5.28.1
      ollama:
        specifier: ^0.5.14
        version: 0.5.14
      openai:
        specifier: ^4.93.0
        version: 4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2)
      pg:
        specifier: 8.11.3
        version: 8.11.3
      redis:
        specifier: ^4.6.13
        version: 4.7.0
      sqlite3:
        specifier: 5.1.7
        version: 5.1.7
      uuid:
        specifier: 9.0.1
        version: 9.0.1
      zod:
        specifier: ^3.24.1
        version: 3.24.2
    devDependencies:
      "@types/node":
        specifier: ^22.7.6
        version: 22.13.5
      "@types/uuid":
        specifier: ^9.0.8
        version: 9.0.8
      dotenv:
        specifier: ^16.4.5
        version: 16.4.7
      fix-tsup-cjs:
        specifier: ^1.2.0
        version: 1.2.0
      jest:
        specifier: ^29.7.0
        version: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      nodemon:
        specifier: ^3.0.1
        version: 3.1.9
      prettier:
        specifier: ^3.5.2
        version: 3.5.2
      rimraf:
        specifier: ^5.0.5
        version: 5.0.10
      ts-jest:
        specifier: ^29.2.6
        version: 29.2.6(@babel/core@7.26.9)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.26.9))(esbuild@0.25.1)(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)))(typescript@5.5.4)
      ts-node:
        specifier: ^10.9.2
        version: 10.9.2(@types/node@22.13.5)(typescript@5.5.4)
      tsup:
        specifier: ^8.3.0
        version: 8.4.0(postcss@8.5.3)(typescript@5.5.4)
      typescript:
        specifier: 5.5.4
        version: 5.5.4

packages:
  "@ampproject/remapping@2.3.0":
    resolution:
      {
        integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
      }
    engines: { node: ">=6.0.0" }

  "@anthropic-ai/sdk@0.40.1":
    resolution:
      {
        integrity: sha512-DJMWm8lTEM9Lk/MSFL+V+ugF7jKOn0M2Ujvb5fN8r2nY14aHbGPZ1k6sgjL+tpJ3VuOGJNG+4R83jEpOuYPv8w==,
      }

  "@babel/code-frame@7.26.2":
    resolution:
      {
        integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/compat-data@7.26.8":
    resolution:
      {
        integrity: sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/core@7.26.9":
    resolution:
      {
        integrity: sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/generator@7.26.9":
    resolution:
      {
        integrity: sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-compilation-targets@7.26.5":
    resolution:
      {
        integrity: sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-imports@7.25.9":
    resolution:
      {
        integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-transforms@7.26.0":
    resolution:
      {
        integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-plugin-utils@7.26.5":
    resolution:
      {
        integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-string-parser@7.25.9":
    resolution:
      {
        integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-identifier@7.25.9":
    resolution:
      {
        integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-option@7.25.9":
    resolution:
      {
        integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helpers@7.26.9":
    resolution:
      {
        integrity: sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/parser@7.26.9":
    resolution:
      {
        integrity: sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==,
      }
    engines: { node: ">=6.0.0" }
    hasBin: true

  "@babel/plugin-syntax-async-generators@7.8.4":
    resolution:
      {
        integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-bigint@7.8.3":
    resolution:
      {
        integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-class-properties@7.12.13":
    resolution:
      {
        integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-class-static-block@7.14.5":
    resolution:
      {
        integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-attributes@7.26.0":
    resolution:
      {
        integrity: sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-meta@7.10.4":
    resolution:
      {
        integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-json-strings@7.8.3":
    resolution:
      {
        integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-jsx@7.25.9":
    resolution:
      {
        integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4":
    resolution:
      {
        integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3":
    resolution:
      {
        integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-numeric-separator@7.10.4":
    resolution:
      {
        integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-object-rest-spread@7.8.3":
    resolution:
      {
        integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-catch-binding@7.8.3":
    resolution:
      {
        integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-chaining@7.8.3":
    resolution:
      {
        integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-private-property-in-object@7.14.5":
    resolution:
      {
        integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-top-level-await@7.14.5":
    resolution:
      {
        integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-typescript@7.25.9":
    resolution:
      {
        integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/template@7.26.9":
    resolution:
      {
        integrity: sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/traverse@7.26.9":
    resolution:
      {
        integrity: sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/types@7.26.9":
    resolution:
      {
        integrity: sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==,
      }
    engines: { node: ">=6.9.0" }

  "@bcoe/v8-coverage@0.2.3":
    resolution:
      {
        integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==,
      }

  "@cfworker/json-schema@4.1.1":
    resolution:
      {
        integrity: sha512-gAmrUZSGtKc3AiBL71iNWxDsyUC5uMaKKGdvzYsBoTW/xi42JQHl7eKV2OYzCUqvc+D2RCcf7EXY2iCyFIk6og==,
      }

  "@cspotcode/source-map-support@0.8.1":
    resolution:
      {
        integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==,
      }
    engines: { node: ">=12" }

  "@esbuild/aix-ppc64@0.25.1":
    resolution:
      {
        integrity: sha512-kfYGy8IdzTGy+z0vFGvExZtxkFlA4zAxgKEahG9KE1ScBjpQnFsNOX8KTU5ojNru5ed5CVoJYXFtoxaq5nFbjQ==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [aix]

  "@esbuild/android-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-50tM0zCJW5kGqgG7fQ7IHvQOcAn9TKiVRuQ/lN0xR+T2lzEFvAi1ZcS8DiksFcEpf1t/GYOeOfCAgDHFpkiSmA==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [android]

  "@esbuild/android-arm@0.25.1":
    resolution:
      {
        integrity: sha512-dp+MshLYux6j/JjdqVLnMglQlFu+MuVeNrmT5nk6q07wNhCdSnB7QZj+7G8VMUGh1q+vj2Bq8kRsuyA00I/k+Q==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [android]

  "@esbuild/android-x64@0.25.1":
    resolution:
      {
        integrity: sha512-GCj6WfUtNldqUzYkN/ITtlhwQqGWu9S45vUXs7EIYf+7rCiiqH9bCloatO9VhxsL0Pji+PF4Lz2XXCES+Q8hDw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [android]

  "@esbuild/darwin-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-5hEZKPf+nQjYoSr/elb62U19/l1mZDdqidGfmFutVUjjUZrOazAtwK+Kr+3y0C/oeJfLlxo9fXb1w7L+P7E4FQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [darwin]

  "@esbuild/darwin-x64@0.25.1":
    resolution:
      {
        integrity: sha512-hxVnwL2Dqs3fM1IWq8Iezh0cX7ZGdVhbTfnOy5uURtao5OIVCEyj9xIzemDi7sRvKsuSdtCAhMKarxqtlyVyfA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [darwin]

  "@esbuild/freebsd-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-1MrCZs0fZa2g8E+FUo2ipw6jw5qqQiH+tERoS5fAfKnRx6NXH31tXBKI3VpmLijLH6yriMZsxJtaXUyFt/8Y4A==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [freebsd]

  "@esbuild/freebsd-x64@0.25.1":
    resolution:
      {
        integrity: sha512-0IZWLiTyz7nm0xuIs0q1Y3QWJC52R8aSXxe40VUxm6BB1RNmkODtW6LHvWRrGiICulcX7ZvyH6h5fqdLu4gkww==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [freebsd]

  "@esbuild/linux-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-jaN3dHi0/DDPelk0nLcXRm1q7DNJpjXy7yWaWvbfkPvI+7XNSc/lDOnCLN7gzsyzgu6qSAmgSvP9oXAhP973uQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [linux]

  "@esbuild/linux-arm@0.25.1":
    resolution:
      {
        integrity: sha512-NdKOhS4u7JhDKw9G3cY6sWqFcnLITn6SqivVArbzIaf3cemShqfLGHYMx8Xlm/lBit3/5d7kXvriTUGa5YViuQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [linux]

  "@esbuild/linux-ia32@0.25.1":
    resolution:
      {
        integrity: sha512-OJykPaF4v8JidKNGz8c/q1lBO44sQNUQtq1KktJXdBLn1hPod5rE/Hko5ugKKZd+D2+o1a9MFGUEIUwO2YfgkQ==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [linux]

  "@esbuild/linux-loong64@0.25.1":
    resolution:
      {
        integrity: sha512-nGfornQj4dzcq5Vp835oM/o21UMlXzn79KobKlcs3Wz9smwiifknLy4xDCLUU0BWp7b/houtdrgUz7nOGnfIYg==,
      }
    engines: { node: ">=18" }
    cpu: [loong64]
    os: [linux]

  "@esbuild/linux-mips64el@0.25.1":
    resolution:
      {
        integrity: sha512-1osBbPEFYwIE5IVB/0g2X6i1qInZa1aIoj1TdL4AaAb55xIIgbg8Doq6a5BzYWgr+tEcDzYH67XVnTmUzL+nXg==,
      }
    engines: { node: ">=18" }
    cpu: [mips64el]
    os: [linux]

  "@esbuild/linux-ppc64@0.25.1":
    resolution:
      {
        integrity: sha512-/6VBJOwUf3TdTvJZ82qF3tbLuWsscd7/1w+D9LH0W/SqUgM5/JJD0lrJ1fVIfZsqB6RFmLCe0Xz3fmZc3WtyVg==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [linux]

  "@esbuild/linux-riscv64@0.25.1":
    resolution:
      {
        integrity: sha512-nSut/Mx5gnilhcq2yIMLMe3Wl4FK5wx/o0QuuCLMtmJn+WeWYoEGDN1ipcN72g1WHsnIbxGXd4i/MF0gTcuAjQ==,
      }
    engines: { node: ">=18" }
    cpu: [riscv64]
    os: [linux]

  "@esbuild/linux-s390x@0.25.1":
    resolution:
      {
        integrity: sha512-cEECeLlJNfT8kZHqLarDBQso9a27o2Zd2AQ8USAEoGtejOrCYHNtKP8XQhMDJMtthdF4GBmjR2au3x1udADQQQ==,
      }
    engines: { node: ">=18" }
    cpu: [s390x]
    os: [linux]

  "@esbuild/linux-x64@0.25.1":
    resolution:
      {
        integrity: sha512-xbfUhu/gnvSEg+EGovRc+kjBAkrvtk38RlerAzQxvMzlB4fXpCFCeUAYzJvrnhFtdeyVCDANSjJvOvGYoeKzFA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [linux]

  "@esbuild/netbsd-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-O96poM2XGhLtpTh+s4+nP7YCCAfb4tJNRVZHfIE7dgmax+yMP2WgMd2OecBuaATHKTHsLWHQeuaxMRnCsH8+5g==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [netbsd]

  "@esbuild/netbsd-x64@0.25.1":
    resolution:
      {
        integrity: sha512-X53z6uXip6KFXBQ+Krbx25XHV/NCbzryM6ehOAeAil7X7oa4XIq+394PWGnwaSQ2WRA0KI6PUO6hTO5zeF5ijA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [netbsd]

  "@esbuild/openbsd-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-Na9T3szbXezdzM/Kfs3GcRQNjHzM6GzFBeU1/6IV/npKP5ORtp9zbQjvkDJ47s6BCgaAZnnnu/cY1x342+MvZg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [openbsd]

  "@esbuild/openbsd-x64@0.25.1":
    resolution:
      {
        integrity: sha512-T3H78X2h1tszfRSf+txbt5aOp/e7TAz3ptVKu9Oyir3IAOFPGV6O9c2naym5TOriy1l0nNf6a4X5UXRZSGX/dw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [openbsd]

  "@esbuild/sunos-x64@0.25.1":
    resolution:
      {
        integrity: sha512-2H3RUvcmULO7dIE5EWJH8eubZAI4xw54H1ilJnRNZdeo8dTADEZ21w6J22XBkXqGJbe0+wnNJtw3UXRoLJnFEg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [sunos]

  "@esbuild/win32-arm64@0.25.1":
    resolution:
      {
        integrity: sha512-GE7XvrdOzrb+yVKB9KsRMq+7a2U/K5Cf/8grVFRAGJmfADr/e/ODQ134RK2/eeHqYV5eQRFxb1hY7Nr15fv1NQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [win32]

  "@esbuild/win32-ia32@0.25.1":
    resolution:
      {
        integrity: sha512-uOxSJCIcavSiT6UnBhBzE8wy3n0hOkJsBOzy7HDAuTDE++1DJMRRVCPGisULScHL+a/ZwdXPpXD3IyFKjA7K8A==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [win32]

  "@esbuild/win32-x64@0.25.1":
    resolution:
      {
        integrity: sha512-Y1EQdcfwMSeQN/ujR5VayLOJ1BHaK+ssyk0AEzPjC+t1lITgsnccPqFjb6V+LsTp/9Iov4ysfjxLaGJ9RPtkVg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [win32]

  "@fastify/busboy@2.1.1":
    resolution:
      {
        integrity: sha512-vBZP4NlzfOlerQTnba4aqZoMhE/a9HY7HRqoOPaETQcSQuWEIyZMHGfVu6w9wGtGK5fED5qRs2DteVCjOH60sA==,
      }
    engines: { node: ">=14" }

  "@gar/promisify@1.1.3":
    resolution:
      {
        integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==,
      }

  "@google/genai@1.2.0":
    resolution:
      {
        integrity: sha512-jAYhzG7UrLJxeQr5cfL87O4AcyEu+E7AA7MJDYPrDWI3Hl25EAdx5mA4AuNfSXZO31LnSyrIkEzkmJOAdlPKOA==,
      }
    engines: { node: ">=20.0.0" }
    peerDependencies:
      "@modelcontextprotocol/sdk": ^1.11.0

  "@isaacs/cliui@8.0.2":
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
      }
    engines: { node: ">=12" }

  "@istanbuljs/load-nyc-config@1.1.0":
    resolution:
      {
        integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==,
      }
    engines: { node: ">=8" }

  "@istanbuljs/schema@0.1.3":
    resolution:
      {
        integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==,
      }
    engines: { node: ">=8" }

  "@jest/console@29.7.0":
    resolution:
      {
        integrity: sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/core@29.7.0":
    resolution:
      {
        integrity: sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  "@jest/environment@29.7.0":
    resolution:
      {
        integrity: sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/expect-utils@29.7.0":
    resolution:
      {
        integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/expect@29.7.0":
    resolution:
      {
        integrity: sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/fake-timers@29.7.0":
    resolution:
      {
        integrity: sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/globals@29.7.0":
    resolution:
      {
        integrity: sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/reporters@29.7.0":
    resolution:
      {
        integrity: sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  "@jest/schemas@29.6.3":
    resolution:
      {
        integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/source-map@29.6.3":
    resolution:
      {
        integrity: sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/test-result@29.7.0":
    resolution:
      {
        integrity: sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/test-sequencer@29.7.0":
    resolution:
      {
        integrity: sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/transform@29.7.0":
    resolution:
      {
        integrity: sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/types@29.6.3":
    resolution:
      {
        integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jridgewell/gen-mapping@0.3.8":
    resolution:
      {
        integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/resolve-uri@3.1.2":
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/set-array@1.2.1":
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/sourcemap-codec@1.5.0":
    resolution:
      {
        integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
      }

  "@jridgewell/trace-mapping@0.3.25":
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
      }

  "@jridgewell/trace-mapping@0.3.9":
    resolution:
      {
        integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==,
      }

  "@langchain/core@0.3.44":
    resolution:
      {
        integrity: sha512-3BsSFf7STvPPZyl2kMANgtVnCUvDdyP4k+koP+nY2Tczd5V+RFkuazIn/JOj/xxy/neZjr4PxFU4BFyF1aKXOA==,
      }
    engines: { node: ">=18" }

  "@mistralai/mistralai@1.5.2":
    resolution:
      {
        integrity: sha512-mBTIDQmuAX9RowMYteZFHJIYlEwDcHzzaxgXzrFtlvH9CkKXK7R1VnZ1sZSe+uLMg0dIXUVdPRUh1SwyFeSqXw==,
      }
    peerDependencies:
      zod: ">= 3"

  "@modelcontextprotocol/sdk@1.12.1":
    resolution:
      {
        integrity: sha512-KG1CZhZfWg+u8pxeM/mByJDScJSrjjxLc8fwQqbsS8xCjBmQfMNEBTotYdNanKekepnfRI85GtgQlctLFpcYPw==,
      }
    engines: { node: ">=18" }

  "@nodelib/fs.scandir@2.1.5":
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.stat@2.0.5":
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.walk@1.2.8":
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
      }
    engines: { node: ">= 8" }

  "@npmcli/fs@1.1.1":
    resolution:
      {
        integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==,
      }

  "@npmcli/move-file@1.1.2":
    resolution:
      {
        integrity: sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==,
      }
    engines: { node: ">=10" }
    deprecated: This functionality has been moved to @npmcli/fs

  "@pkgjs/parseargs@0.11.0":
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
      }
    engines: { node: ">=14" }

  "@qdrant/js-client-rest@1.13.0":
    resolution:
      {
        integrity: sha512-bewMtnXlGvhhnfXsp0sLoLXOGvnrCM15z9lNlG0Snp021OedNAnRtKkerjk5vkOcbQWUmJHXYCuxDfcT93aSkA==,
      }
    engines: { node: ">=18.0.0", pnpm: ">=8" }
    peerDependencies:
      typescript: ">=4.7"

  "@qdrant/openapi-typescript-fetch@1.2.6":
    resolution:
      {
        integrity: sha512-oQG/FejNpItrxRHoyctYvT3rwGZOnK4jr3JdppO/c78ktDvkWiPXPHNsrDf33K9sZdRb6PR7gi4noIapu5q4HA==,
      }
    engines: { node: ">=18.0.0", pnpm: ">=8" }

  "@redis/bloom@1.2.0":
    resolution:
      {
        integrity: sha512-HG2DFjYKbpNmVXsa0keLHp/3leGJz1mjh09f2RLGGLQZzSHpkmZWuwJbAvo3QcRY8p80m5+ZdXZdYOSBLlp7Cg==,
      }
    peerDependencies:
      "@redis/client": ^1.0.0

  "@redis/client@1.6.0":
    resolution:
      {
        integrity: sha512-aR0uffYI700OEEH4gYnitAnv3vzVGXCFvYfdpu/CJKvk4pHfLPEy/JSZyrpQ+15WhXe1yJRXLtfQ84s4mEXnPg==,
      }
    engines: { node: ">=14" }

  "@redis/graph@1.1.1":
    resolution:
      {
        integrity: sha512-FEMTcTHZozZciLRl6GiiIB4zGm5z5F3F6a6FZCyrfxdKOhFlGkiAqlexWMBzCi4DcRoyiOsuLfW+cjlGWyExOw==,
      }
    peerDependencies:
      "@redis/client": ^1.0.0

  "@redis/json@1.0.7":
    resolution:
      {
        integrity: sha512-6UyXfjVaTBTJtKNG4/9Z8PSpKE6XgSyEb8iwaqDcy+uKrd/DGYHTWkUdnQDyzm727V7p21WUMhsqz5oy65kPcQ==,
      }
    peerDependencies:
      "@redis/client": ^1.0.0

  "@redis/search@1.2.0":
    resolution:
      {
        integrity: sha512-tYoDBbtqOVigEDMAcTGsRlMycIIjwMCgD8eR2t0NANeQmgK/lvxNAvYyb6bZDD4frHRhIHkJu2TBRvB0ERkOmw==,
      }
    peerDependencies:
      "@redis/client": ^1.0.0

  "@redis/time-series@1.1.0":
    resolution:
      {
        integrity: sha512-c1Q99M5ljsIuc4YdaCwfUEXsofakb9c8+Zse2qxTadu8TalLXuAESzLvFAvNVbkmSlvlzIQOLpBCmWI9wTOt+g==,
      }
    peerDependencies:
      "@redis/client": ^1.0.0

  "@rollup/rollup-android-arm-eabi@4.37.0":
    resolution:
      {
        integrity: sha512-l7StVw6WAa8l3vA1ov80jyetOAEo1FtHvZDbzXDO/02Sq/QVvqlHkYoFwDJPIMj0GKiistsBudfx5tGFnwYWDQ==,
      }
    cpu: [arm]
    os: [android]

  "@rollup/rollup-android-arm64@4.37.0":
    resolution:
      {
        integrity: sha512-6U3SlVyMxezt8Y+/iEBcbp945uZjJwjZimu76xoG7tO1av9VO691z8PkhzQ85ith2I8R2RddEPeSfcbyPfD4hA==,
      }
    cpu: [arm64]
    os: [android]

  "@rollup/rollup-darwin-arm64@4.37.0":
    resolution:
      {
        integrity: sha512-+iTQ5YHuGmPt10NTzEyMPbayiNTcOZDWsbxZYR1ZnmLnZxG17ivrPSWFO9j6GalY0+gV3Jtwrrs12DBscxnlYA==,
      }
    cpu: [arm64]
    os: [darwin]

  "@rollup/rollup-darwin-x64@4.37.0":
    resolution:
      {
        integrity: sha512-m8W2UbxLDcmRKVjgl5J/k4B8d7qX2EcJve3Sut7YGrQoPtCIQGPH5AMzuFvYRWZi0FVS0zEY4c8uttPfX6bwYQ==,
      }
    cpu: [x64]
    os: [darwin]

  "@rollup/rollup-freebsd-arm64@4.37.0":
    resolution:
      {
        integrity: sha512-FOMXGmH15OmtQWEt174v9P1JqqhlgYge/bUjIbiVD1nI1NeJ30HYT9SJlZMqdo1uQFyt9cz748F1BHghWaDnVA==,
      }
    cpu: [arm64]
    os: [freebsd]

  "@rollup/rollup-freebsd-x64@4.37.0":
    resolution:
      {
        integrity: sha512-SZMxNttjPKvV14Hjck5t70xS3l63sbVwl98g3FlVVx2YIDmfUIy29jQrsw06ewEYQ8lQSuY9mpAPlmgRD2iSsA==,
      }
    cpu: [x64]
    os: [freebsd]

  "@rollup/rollup-linux-arm-gnueabihf@4.37.0":
    resolution:
      {
        integrity: sha512-hhAALKJPidCwZcj+g+iN+38SIOkhK2a9bqtJR+EtyxrKKSt1ynCBeqrQy31z0oWU6thRZzdx53hVgEbRkuI19w==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm-musleabihf@4.37.0":
    resolution:
      {
        integrity: sha512-jUb/kmn/Gd8epbHKEqkRAxq5c2EwRt0DqhSGWjPFxLeFvldFdHQs/n8lQ9x85oAeVb6bHcS8irhTJX2FCOd8Ag==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm64-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-oNrJxcQT9IcbcmKlkF+Yz2tmOxZgG9D9GRq+1OE6XCQwCVwxixYAa38Z8qqPzQvzt1FCfmrHX03E0pWoXm1DqA==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-arm64-musl@4.37.0":
    resolution:
      {
        integrity: sha512-pfxLBMls+28Ey2enpX3JvjEjaJMBX5XlPCZNGxj4kdJyHduPBXtxYeb8alo0a7bqOoWZW2uKynhHxF/MWoHaGQ==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-loongarch64-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-yCE0NnutTC/7IGUq/PUHmoeZbIwq3KRh02e9SfFh7Vmc1Z7atuJRYWhRME5fKgT8aS20mwi1RyChA23qSyRGpA==,
      }
    cpu: [loong64]
    os: [linux]

  "@rollup/rollup-linux-powerpc64le-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-NxcICptHk06E2Lh3a4Pu+2PEdZ6ahNHuK7o6Np9zcWkrBMuv21j10SQDJW3C9Yf/A/P7cutWoC/DptNLVsZ0VQ==,
      }
    cpu: [ppc64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-PpWwHMPCVpFZLTfLq7EWJWvrmEuLdGn1GMYcm5MV7PaRgwCEYJAwiN94uBuZev0/J/hFIIJCsYw4nLmXA9J7Pw==,
      }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-musl@4.37.0":
    resolution:
      {
        integrity: sha512-DTNwl6a3CfhGTAOYZ4KtYbdS8b+275LSLqJVJIrPa5/JuIufWWZ/QFvkxp52gpmguN95eujrM68ZG+zVxa8zHA==,
      }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-s390x-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-hZDDU5fgWvDdHFuExN1gBOhCuzo/8TMpidfOR+1cPZJflcEzXdCy1LjnklQdW8/Et9sryOPJAKAQRw8Jq7Tg+A==,
      }
    cpu: [s390x]
    os: [linux]

  "@rollup/rollup-linux-x64-gnu@4.37.0":
    resolution:
      {
        integrity: sha512-pKivGpgJM5g8dwj0ywBwe/HeVAUSuVVJhUTa/URXjxvoyTT/AxsLTAbkHkDHG7qQxLoW2s3apEIl26uUe08LVQ==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-linux-x64-musl@4.37.0":
    resolution:
      {
        integrity: sha512-E2lPrLKE8sQbY/2bEkVTGDEk4/49UYRVWgj90MY8yPjpnGBQ+Xi1Qnr7b7UIWw1NOggdFQFOLZ8+5CzCiz143w==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-win32-arm64-msvc@4.37.0":
    resolution:
      {
        integrity: sha512-Jm7biMazjNzTU4PrQtr7VS8ibeys9Pn29/1bm4ph7CP2kf21950LgN+BaE2mJ1QujnvOc6p54eWWiVvn05SOBg==,
      }
    cpu: [arm64]
    os: [win32]

  "@rollup/rollup-win32-ia32-msvc@4.37.0":
    resolution:
      {
        integrity: sha512-e3/1SFm1OjefWICB2Ucstg2dxYDkDTZGDYgwufcbsxTHyqQps1UQf33dFEChBNmeSsTOyrjw2JJq0zbG5GF6RA==,
      }
    cpu: [ia32]
    os: [win32]

  "@rollup/rollup-win32-x64-msvc@4.37.0":
    resolution:
      {
        integrity: sha512-LWbXUBwn/bcLx2sSsqy7pK5o+Nr+VCoRoAohfJ5C/aBio9nfJmGQqHAhU6pwxV/RmyTk5AqdySma7uwWGlmeuA==,
      }
    cpu: [x64]
    os: [win32]

  "@sevinf/maybe@0.5.0":
    resolution:
      {
        integrity: sha512-ARhyoYDnY1LES3vYI0fiG6e9esWfTNcXcO6+MPJJXcnyMV3bim4lnFt45VXouV7y82F4x3YH8nOQ6VztuvUiWg==,
      }

  "@sinclair/typebox@0.27.8":
    resolution:
      {
        integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==,
      }

  "@sinonjs/commons@3.0.1":
    resolution:
      {
        integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==,
      }

  "@sinonjs/fake-timers@10.3.0":
    resolution:
      {
        integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==,
      }

  "@supabase/auth-js@2.68.0":
    resolution:
      {
        integrity: sha512-odG7nb7aOmZPUXk6SwL2JchSsn36Ppx11i2yWMIc/meUO2B2HK9YwZHPK06utD9Ql9ke7JKDbwGin/8prHKxxQ==,
      }

  "@supabase/functions-js@2.4.4":
    resolution:
      {
        integrity: sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==,
      }

  "@supabase/node-fetch@2.6.15":
    resolution:
      {
        integrity: sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==,
      }
    engines: { node: 4.x || >=6.0.0 }

  "@supabase/postgrest-js@1.19.2":
    resolution:
      {
        integrity: sha512-MXRbk4wpwhWl9IN6rIY1mR8uZCCG4MZAEji942ve6nMwIqnBgBnZhZlON6zTTs6fgveMnoCILpZv1+K91jN+ow==,
      }

  "@supabase/realtime-js@2.11.2":
    resolution:
      {
        integrity: sha512-u/XeuL2Y0QEhXSoIPZZwR6wMXgB+RQbJzG9VErA3VghVt7uRfSVsjeqd7m5GhX3JR6dM/WRmLbVR8URpDWG4+w==,
      }

  "@supabase/storage-js@2.7.1":
    resolution:
      {
        integrity: sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==,
      }

  "@supabase/supabase-js@2.49.1":
    resolution:
      {
        integrity: sha512-lKaptKQB5/juEF5+jzmBeZlz69MdHZuxf+0f50NwhL+IE//m4ZnOeWlsKRjjsM0fVayZiQKqLvYdBn0RLkhGiQ==,
      }

  "@tootallnate/once@1.1.2":
    resolution:
      {
        integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==,
      }
    engines: { node: ">= 6" }

  "@tsconfig/node10@1.0.11":
    resolution:
      {
        integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==,
      }

  "@tsconfig/node12@1.0.11":
    resolution:
      {
        integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==,
      }

  "@tsconfig/node14@1.0.3":
    resolution:
      {
        integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==,
      }

  "@tsconfig/node16@1.0.4":
    resolution:
      {
        integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==,
      }

  "@types/babel__core@7.20.5":
    resolution:
      {
        integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==,
      }

  "@types/babel__generator@7.6.8":
    resolution:
      {
        integrity: sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==,
      }

  "@types/babel__template@7.4.4":
    resolution:
      {
        integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==,
      }

  "@types/babel__traverse@7.20.6":
    resolution:
      {
        integrity: sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==,
      }

  "@types/estree@1.0.6":
    resolution:
      {
        integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==,
      }

  "@types/graceful-fs@4.1.9":
    resolution:
      {
        integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==,
      }

  "@types/istanbul-lib-coverage@2.0.6":
    resolution:
      {
        integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==,
      }

  "@types/istanbul-lib-report@3.0.3":
    resolution:
      {
        integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==,
      }

  "@types/istanbul-reports@3.0.4":
    resolution:
      {
        integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==,
      }

  "@types/jest@29.5.14":
    resolution:
      {
        integrity: sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==,
      }

  "@types/node-fetch@2.6.12":
    resolution:
      {
        integrity: sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==,
      }

  "@types/node@18.19.76":
    resolution:
      {
        integrity: sha512-yvR7Q9LdPz2vGpmpJX5LolrgRdWvB67MJKDPSgIIzpFbaf9a1j/f5DnLp5VDyHGMR0QZHlTr1afsD87QCXFHKw==,
      }

  "@types/node@22.13.5":
    resolution:
      {
        integrity: sha512-+lTU0PxZXn0Dr1NBtC7Y8cR21AJr87dLLU953CWA6pMxxv/UDc7jYAY90upcrie1nRcD6XNG5HOYEDtgW5TxAg==,
      }

  "@types/normalize-package-data@2.4.4":
    resolution:
      {
        integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==,
      }

  "@types/pg@8.11.0":
    resolution:
      {
        integrity: sha512-sDAlRiBNthGjNFfvt0k6mtotoVYVQ63pA8R4EMWka7crawSR60waVYR0HAgmPRs/e2YaeJTD/43OoZ3PFw80pw==,
      }

  "@types/phoenix@1.6.6":
    resolution:
      {
        integrity: sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==,
      }

  "@types/retry@0.12.0":
    resolution:
      {
        integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==,
      }

  "@types/sqlite3@3.1.11":
    resolution:
      {
        integrity: sha512-KYF+QgxAnnAh7DWPdNDroxkDI3/MspH1NMx6m/N/6fT1G6+jvsw4/ZePt8R8cr7ta58aboeTfYFBDxTJ5yv15w==,
      }

  "@types/stack-utils@2.0.3":
    resolution:
      {
        integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==,
      }

  "@types/uuid@10.0.0":
    resolution:
      {
        integrity: sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==,
      }

  "@types/uuid@9.0.8":
    resolution:
      {
        integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==,
      }

  "@types/ws@8.18.0":
    resolution:
      {
        integrity: sha512-8svvI3hMyvN0kKCJMvTJP/x6Y/EoQbepff882wL+Sn5QsXb3etnamgrJq4isrBxSJj5L2AuXcI0+bgkoAXGUJw==,
      }

  "@types/yargs-parser@21.0.3":
    resolution:
      {
        integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==,
      }

  "@types/yargs@17.0.33":
    resolution:
      {
        integrity: sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==,
      }

  abbrev@1.1.1:
    resolution:
      {
        integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==,
      }

  abort-controller@3.0.0:
    resolution:
      {
        integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==,
      }
    engines: { node: ">=6.5" }

  accepts@2.0.0:
    resolution:
      {
        integrity: sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==,
      }
    engines: { node: ">= 0.6" }

  acorn-walk@8.3.4:
    resolution:
      {
        integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==,
      }
    engines: { node: ">=0.4.0" }

  acorn@8.14.0:
    resolution:
      {
        integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==,
      }
    engines: { node: ">=0.4.0" }
    hasBin: true

  agent-base@6.0.2:
    resolution:
      {
        integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==,
      }
    engines: { node: ">= 6.0.0" }

  agent-base@7.1.3:
    resolution:
      {
        integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==,
      }
    engines: { node: ">= 14" }

  agentkeepalive@4.6.0:
    resolution:
      {
        integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==,
      }
    engines: { node: ">= 8.0.0" }

  aggregate-error@3.1.0:
    resolution:
      {
        integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==,
      }
    engines: { node: ">=8" }

  ajv@6.12.6:
    resolution:
      {
        integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==,
      }

  ansi-escapes@4.3.2:
    resolution:
      {
        integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@6.1.0:
    resolution:
      {
        integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
      }
    engines: { node: ">=12" }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
      }
    engines: { node: ">=8" }

  ansi-styles@5.2.0:
    resolution:
      {
        integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==,
      }
    engines: { node: ">=10" }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
      }
    engines: { node: ">=12" }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
      }
    engines: { node: ">= 8" }

  aproba@2.0.0:
    resolution:
      {
        integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==,
      }

  are-we-there-yet@3.0.1:
    resolution:
      {
        integrity: sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.

  arg@4.1.3:
    resolution:
      {
        integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==,
      }

  argparse@1.0.10:
    resolution:
      {
        integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==,
      }

  async@3.2.6:
    resolution:
      {
        integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==,
      }

  asynckit@0.4.0:
    resolution:
      {
        integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
      }

  axios@1.7.7:
    resolution:
      {
        integrity: sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==,
      }

  babel-jest@29.7.0:
    resolution:
      {
        integrity: sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@babel/core": ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution:
      {
        integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==,
      }
    engines: { node: ">=8" }

  babel-plugin-jest-hoist@29.6.3:
    resolution:
      {
        integrity: sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  babel-preset-current-node-syntax@1.1.0:
    resolution:
      {
        integrity: sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0

  babel-preset-jest@29.6.3:
    resolution:
      {
        integrity: sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@babel/core": ^7.0.0

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
      }

  base-64@0.1.0:
    resolution:
      {
        integrity: sha512-Y5gU45svrR5tI2Vt/X9GPd3L0HNIKzGu202EjxrXMpuc2V2CiKgemAbUUsqYmZJvPtCXoUKjNZwBJzsNScUbXA==,
      }

  base64-js@1.5.1:
    resolution:
      {
        integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==,
      }

  bignumber.js@9.2.0:
    resolution:
      {
        integrity: sha512-JocpCSOixzy5XFJi2ub6IMmV/G9i8Lrm2lZvwBv9xPdglmZM0ufDVBbjbrfU/zuLvBfD7Bv2eYxz9i+OHTgkew==,
      }
    deprecated: pkg version number incorrect

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
      }
    engines: { node: ">=8" }

  bindings@1.5.0:
    resolution:
      {
        integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==,
      }

  bl@4.1.0:
    resolution:
      {
        integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==,
      }

  body-parser@2.2.0:
    resolution:
      {
        integrity: sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==,
      }
    engines: { node: ">=18" }

  brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
      }

  brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
      }

  braces@3.0.3:
    resolution:
      {
        integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
      }
    engines: { node: ">=8" }

  browserslist@4.24.4:
    resolution:
      {
        integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==,
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  bs-logger@0.2.6:
    resolution:
      {
        integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==,
      }
    engines: { node: ">= 6" }

  bser@2.1.1:
    resolution:
      {
        integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==,
      }

  buffer-equal-constant-time@1.0.1:
    resolution:
      {
        integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==,
      }

  buffer-from@1.1.2:
    resolution:
      {
        integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==,
      }

  buffer-writer@2.0.0:
    resolution:
      {
        integrity: sha512-a7ZpuTZU1TRtnwyCNW3I5dc0wWNC3VR9S++Ewyk2HHZdrO3CQJqSpd+95Us590V6AL7JqUAH2IwZ/398PmNFgw==,
      }
    engines: { node: ">=4" }

  buffer@5.7.1:
    resolution:
      {
        integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==,
      }

  buffer@6.0.3:
    resolution:
      {
        integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==,
      }

  bundle-require@5.1.0:
    resolution:
      {
        integrity: sha512-3WrrOuZiyaaZPWiEt4G3+IffISVC9HYlWueJEBWED4ZH4aIAC2PnkdnuRrR94M+w6yGWn4AglWtJtBI8YqvgoA==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }
    peerDependencies:
      esbuild: ">=0.18"

  bytes@3.1.2:
    resolution:
      {
        integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==,
      }
    engines: { node: ">= 0.8" }

  cac@6.7.14:
    resolution:
      {
        integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
      }
    engines: { node: ">=8" }

  cacache@15.3.0:
    resolution:
      {
        integrity: sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==,
      }
    engines: { node: ">= 10" }

  call-bind-apply-helpers@1.0.2:
    resolution:
      {
        integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==,
      }
    engines: { node: ">= 0.4" }

  call-bound@1.0.4:
    resolution:
      {
        integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==,
      }
    engines: { node: ">= 0.4" }

  callsites@3.1.0:
    resolution:
      {
        integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
      }
    engines: { node: ">=6" }

  camelcase@5.3.1:
    resolution:
      {
        integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==,
      }
    engines: { node: ">=6" }

  camelcase@6.3.0:
    resolution:
      {
        integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==,
      }
    engines: { node: ">=10" }

  caniuse-lite@1.0.30001701:
    resolution:
      {
        integrity: sha512-faRs/AW3jA9nTwmJBSO1PQ6L/EOgsB5HMQQq4iCu5zhPgVVgO/pZRHlmatwijZKetFw8/Pr4q6dEN8sJuq8qTw==,
      }

  chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
      }
    engines: { node: ">=10" }

  char-regex@1.0.2:
    resolution:
      {
        integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==,
      }
    engines: { node: ">=10" }

  charenc@0.0.2:
    resolution:
      {
        integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==,
      }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
      }
    engines: { node: ">= 8.10.0" }

  chokidar@4.0.3:
    resolution:
      {
        integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==,
      }
    engines: { node: ">= 14.16.0" }

  chownr@1.1.4:
    resolution:
      {
        integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==,
      }

  chownr@2.0.0:
    resolution:
      {
        integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==,
      }
    engines: { node: ">=10" }

  ci-info@3.9.0:
    resolution:
      {
        integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==,
      }
    engines: { node: ">=8" }

  cjs-module-lexer@1.4.3:
    resolution:
      {
        integrity: sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==,
      }

  clean-stack@2.2.0:
    resolution:
      {
        integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==,
      }
    engines: { node: ">=6" }

  cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
      }
    engines: { node: ">=12" }

  cluster-key-slot@1.1.2:
    resolution:
      {
        integrity: sha512-RMr0FhtfXemyinomL4hrWcYJxmX6deFdCxpJzhDttxgO1+bcCnkk+9drydLVDmAMG7NE6aN/fl4F7ucU/90gAA==,
      }
    engines: { node: ">=0.10.0" }

  co@4.6.0:
    resolution:
      {
        integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==,
      }
    engines: { iojs: ">= 1.0.0", node: ">= 0.12.0" }

  collect-v8-coverage@1.0.2:
    resolution:
      {
        integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==,
      }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
      }
    engines: { node: ">=7.0.0" }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
      }

  color-support@1.1.3:
    resolution:
      {
        integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==,
      }
    hasBin: true

  combined-stream@1.0.8:
    resolution:
      {
        integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
      }
    engines: { node: ">= 0.8" }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
      }
    engines: { node: ">= 6" }

  concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
      }

  consola@3.4.2:
    resolution:
      {
        integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==,
      }
    engines: { node: ^14.18.0 || >=16.10.0 }

  console-control-strings@1.1.0:
    resolution:
      {
        integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==,
      }

  console-table-printer@2.12.1:
    resolution:
      {
        integrity: sha512-wKGOQRRvdnd89pCeH96e2Fn4wkbenSP6LMHfjfyNLMbGuHEFbMqQNuxXqd0oXG9caIOQ1FTvc5Uijp9/4jujnQ==,
      }

  content-disposition@1.0.0:
    resolution:
      {
        integrity: sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==,
      }
    engines: { node: ">= 0.6" }

  content-type@1.0.5:
    resolution:
      {
        integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==,
      }
    engines: { node: ">= 0.6" }

  convert-source-map@2.0.0:
    resolution:
      {
        integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
      }

  cookie-signature@1.2.2:
    resolution:
      {
        integrity: sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==,
      }
    engines: { node: ">=6.6.0" }

  cookie@0.7.2:
    resolution:
      {
        integrity: sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==,
      }
    engines: { node: ">= 0.6" }

  cors@2.8.5:
    resolution:
      {
        integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==,
      }
    engines: { node: ">= 0.10" }

  create-jest@29.7.0:
    resolution:
      {
        integrity: sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true

  create-require@1.1.1:
    resolution:
      {
        integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==,
      }

  cross-spawn@7.0.6:
    resolution:
      {
        integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
      }
    engines: { node: ">= 8" }

  crypt@0.0.2:
    resolution:
      {
        integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==,
      }

  debug@4.4.0:
    resolution:
      {
        integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==,
      }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution:
      {
        integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==,
      }
    engines: { node: ">=0.10.0" }

  decompress-response@6.0.0:
    resolution:
      {
        integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==,
      }
    engines: { node: ">=10" }

  dedent@1.5.3:
    resolution:
      {
        integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==,
      }
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-extend@0.6.0:
    resolution:
      {
        integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==,
      }
    engines: { node: ">=4.0.0" }

  deepmerge@4.3.1:
    resolution:
      {
        integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==,
      }
    engines: { node: ">=0.10.0" }

  delayed-stream@1.0.0:
    resolution:
      {
        integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
      }
    engines: { node: ">=0.4.0" }

  delegates@1.0.0:
    resolution:
      {
        integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==,
      }

  depd@2.0.0:
    resolution:
      {
        integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==,
      }
    engines: { node: ">= 0.8" }

  detect-libc@2.0.3:
    resolution:
      {
        integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==,
      }
    engines: { node: ">=8" }

  detect-newline@3.1.0:
    resolution:
      {
        integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==,
      }
    engines: { node: ">=8" }

  diff-sequences@29.6.3:
    resolution:
      {
        integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  diff@4.0.2:
    resolution:
      {
        integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==,
      }
    engines: { node: ">=0.3.1" }

  digest-fetch@1.3.0:
    resolution:
      {
        integrity: sha512-CGJuv6iKNM7QyZlM2T3sPAdZWd/p9zQiRNS9G+9COUCwzWFTs0Xp8NF5iePx7wtvhDykReiRRrSeNb4oMmB8lA==,
      }

  dotenv@16.4.7:
    resolution:
      {
        integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==,
      }
    engines: { node: ">=12" }

  dunder-proto@1.0.1:
    resolution:
      {
        integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==,
      }
    engines: { node: ">= 0.4" }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
      }

  ecdsa-sig-formatter@1.0.11:
    resolution:
      {
        integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==,
      }

  ee-first@1.1.1:
    resolution:
      {
        integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==,
      }

  ejs@3.1.10:
    resolution:
      {
        integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==,
      }
    engines: { node: ">=0.10.0" }
    hasBin: true

  electron-to-chromium@1.5.109:
    resolution:
      {
        integrity: sha512-AidaH9JETVRr9DIPGfp1kAarm/W6hRJTPuCnkF+2MqhF4KaAgRIcBc8nvjk+YMXZhwfISof/7WG29eS4iGxQLQ==,
      }

  emittery@0.13.1:
    resolution:
      {
        integrity: sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==,
      }
    engines: { node: ">=12" }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
      }

  encodeurl@2.0.0:
    resolution:
      {
        integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==,
      }
    engines: { node: ">= 0.8" }

  encoding@0.1.13:
    resolution:
      {
        integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==,
      }

  end-of-stream@1.4.4:
    resolution:
      {
        integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==,
      }

  env-paths@2.2.1:
    resolution:
      {
        integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==,
      }
    engines: { node: ">=6" }

  err-code@2.0.3:
    resolution:
      {
        integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==,
      }

  error-ex@1.3.2:
    resolution:
      {
        integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==,
      }

  es-define-property@1.0.1:
    resolution:
      {
        integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==,
      }
    engines: { node: ">= 0.4" }

  es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
      }
    engines: { node: ">= 0.4" }

  es-object-atoms@1.1.1:
    resolution:
      {
        integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==,
      }
    engines: { node: ">= 0.4" }

  es-set-tostringtag@2.1.0:
    resolution:
      {
        integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==,
      }
    engines: { node: ">= 0.4" }

  esbuild@0.25.1:
    resolution:
      {
        integrity: sha512-BGO5LtrGC7vxnqucAe/rmvKdJllfGaYWdyABvyMoXQlfYMb2bbRuReWR5tEGE//4LcNJj9XrkovTqNYRFZHAMQ==,
      }
    engines: { node: ">=18" }
    hasBin: true

  escalade@3.2.0:
    resolution:
      {
        integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==,
      }
    engines: { node: ">=6" }

  escape-html@1.0.3:
    resolution:
      {
        integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==,
      }

  escape-string-regexp@2.0.0:
    resolution:
      {
        integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==,
      }
    engines: { node: ">=8" }

  esprima@4.0.1:
    resolution:
      {
        integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==,
      }
    engines: { node: ">=4" }
    hasBin: true

  etag@1.8.1:
    resolution:
      {
        integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==,
      }
    engines: { node: ">= 0.6" }

  event-target-shim@5.0.1:
    resolution:
      {
        integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==,
      }
    engines: { node: ">=6" }

  eventemitter3@4.0.7:
    resolution:
      {
        integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==,
      }

  eventsource-parser@3.0.2:
    resolution:
      {
        integrity: sha512-6RxOBZ/cYgd8usLwsEl+EC09Au/9BcmCKYF2/xbml6DNczf7nv0MQb+7BA2F+li6//I+28VNlQR37XfQtcAJuA==,
      }
    engines: { node: ">=18.0.0" }

  eventsource@3.0.7:
    resolution:
      {
        integrity: sha512-CRT1WTyuQoD771GW56XEZFQ/ZoSfWid1alKGDYMmkt2yl8UXrVR4pspqWNEcqKvVIzg6PAltWjxcSSPrboA4iA==,
      }
    engines: { node: ">=18.0.0" }

  execa@5.1.1:
    resolution:
      {
        integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==,
      }
    engines: { node: ">=10" }

  exit@0.1.2:
    resolution:
      {
        integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==,
      }
    engines: { node: ">= 0.8.0" }

  expand-template@2.0.3:
    resolution:
      {
        integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==,
      }
    engines: { node: ">=6" }

  expect@29.7.0:
    resolution:
      {
        integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  express-rate-limit@7.5.0:
    resolution:
      {
        integrity: sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg==,
      }
    engines: { node: ">= 16" }
    peerDependencies:
      express: ^4.11 || 5 || ^5.0.0-beta.1

  express@5.1.0:
    resolution:
      {
        integrity: sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==,
      }
    engines: { node: ">= 18" }

  extend@3.0.2:
    resolution:
      {
        integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==,
      }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
      }

  fast-glob@3.3.3:
    resolution:
      {
        integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==,
      }
    engines: { node: ">=8.6.0" }

  fast-json-stable-stringify@2.1.0:
    resolution:
      {
        integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
      }

  fastq@1.19.1:
    resolution:
      {
        integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==,
      }

  fb-watchman@2.0.2:
    resolution:
      {
        integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==,
      }

  fdir@6.4.3:
    resolution:
      {
        integrity: sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==,
      }
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-uri-to-path@1.0.0:
    resolution:
      {
        integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==,
      }

  filelist@1.0.4:
    resolution:
      {
        integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==,
      }

  fill-range@7.1.1:
    resolution:
      {
        integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
      }
    engines: { node: ">=8" }

  finalhandler@2.1.0:
    resolution:
      {
        integrity: sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==,
      }
    engines: { node: ">= 0.8" }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==,
      }
    engines: { node: ">=8" }

  fix-tsup-cjs@1.2.0:
    resolution:
      {
        integrity: sha512-5z2nZxrnKxk+jLq5TyD0xbPXI2I18FF+knIZVG55e0CXWgXF/F4SpCBsiW7JTBPwghqXsC66T2yctnVT/sMO0g==,
      }
    hasBin: true

  follow-redirects@1.15.9:
    resolution:
      {
        integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==,
      }
    engines: { node: ">=4.0" }
    peerDependencies:
      debug: "*"
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.3.1:
    resolution:
      {
        integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==,
      }
    engines: { node: ">=14" }

  form-data-encoder@1.7.2:
    resolution:
      {
        integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==,
      }

  form-data@4.0.2:
    resolution:
      {
        integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==,
      }
    engines: { node: ">= 6" }

  formdata-node@4.4.1:
    resolution:
      {
        integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==,
      }
    engines: { node: ">= 12.20" }

  forwarded@0.2.0:
    resolution:
      {
        integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==,
      }
    engines: { node: ">= 0.6" }

  fresh@2.0.0:
    resolution:
      {
        integrity: sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==,
      }
    engines: { node: ">= 0.8" }

  fs-constants@1.0.0:
    resolution:
      {
        integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==,
      }

  fs-minipass@2.1.0:
    resolution:
      {
        integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==,
      }
    engines: { node: ">= 8" }

  fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==,
      }

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
      }

  gauge@4.0.4:
    resolution:
      {
        integrity: sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.

  gaxios@6.7.1:
    resolution:
      {
        integrity: sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==,
      }
    engines: { node: ">=14" }

  gcp-metadata@6.1.1:
    resolution:
      {
        integrity: sha512-a4tiq7E0/5fTjxPAaH4jpjkSv/uCaU2p5KC6HVGrvl0cDjA8iBZv4vv1gyzlmK0ZUKqwpOyQMKzZQe3lTit77A==,
      }
    engines: { node: ">=14" }

  generic-pool@3.9.0:
    resolution:
      {
        integrity: sha512-hymDOu5B53XvN4QT9dBmZxPX4CWhBPPLguTZ9MMFeFa/Kg0xWVfylOVNlJji/E7yTZWFd/q9GO5TxDLq156D7g==,
      }
    engines: { node: ">= 4" }

  gensync@1.0.0-beta.2:
    resolution:
      {
        integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
      }
    engines: { node: ">=6.9.0" }

  get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
      }
    engines: { node: 6.* || 8.* || >= 10.* }

  get-intrinsic@1.3.0:
    resolution:
      {
        integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==,
      }
    engines: { node: ">= 0.4" }

  get-package-type@0.1.0:
    resolution:
      {
        integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==,
      }
    engines: { node: ">=8.0.0" }

  get-proto@1.0.1:
    resolution:
      {
        integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==,
      }
    engines: { node: ">= 0.4" }

  get-stream@6.0.1:
    resolution:
      {
        integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==,
      }
    engines: { node: ">=10" }

  github-from-package@0.0.0:
    resolution:
      {
        integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==,
      }

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
      }
    engines: { node: ">= 6" }

  glob@10.4.5:
    resolution:
      {
        integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
      }
    hasBin: true

  glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==,
      }
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
      }
    engines: { node: ">=4" }

  google-auth-library@9.15.1:
    resolution:
      {
        integrity: sha512-Jb6Z0+nvECVz+2lzSMt9u98UsoakXxA2HGHMCxh+so3n90XgYWkq5dur19JAJV7ONiJY22yBTyJB1TSkvPq9Ng==,
      }
    engines: { node: ">=14" }

  google-logging-utils@0.0.2:
    resolution:
      {
        integrity: sha512-NEgUnEcBiP5HrPzufUkBzJOD/Sxsco3rLNo1F1TNf7ieU8ryUzBhqba8r756CjLX7rn3fHl6iLEwPYuqpoKgQQ==,
      }
    engines: { node: ">=14" }

  gopd@1.2.0:
    resolution:
      {
        integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==,
      }
    engines: { node: ">= 0.4" }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
      }

  groq-sdk@0.3.0:
    resolution:
      {
        integrity: sha512-Cdgjh4YoSBE2X4S9sxPGXaAy1dlN4bRtAaDZ3cnq+XsxhhN9WSBeHF64l7LWwuD5ntmw7YC5Vf4Ff1oHCg1LOg==,
      }

  gtoken@7.1.0:
    resolution:
      {
        integrity: sha512-pCcEwRi+TKpMlxAQObHDQ56KawURgyAf6jtIY046fJ5tIv3zDe/LEIubckAO8fj6JnAxLdmWkUfNyulQ2iKdEw==,
      }
    engines: { node: ">=14.0.0" }

  has-flag@3.0.0:
    resolution:
      {
        integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==,
      }
    engines: { node: ">=4" }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
      }
    engines: { node: ">=8" }

  has-symbols@1.1.0:
    resolution:
      {
        integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==,
      }
    engines: { node: ">= 0.4" }

  has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
      }
    engines: { node: ">= 0.4" }

  has-unicode@2.0.1:
    resolution:
      {
        integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==,
      }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
      }
    engines: { node: ">= 0.4" }

  hosted-git-info@7.0.2:
    resolution:
      {
        integrity: sha512-puUZAUKT5m8Zzvs72XWy3HtvVbTWljRE66cP60bxJzAqf2DgICo7lYTY2IHUmLnNpjYvw5bvmoHvPc0QO2a62w==,
      }
    engines: { node: ^16.14.0 || >=18.0.0 }

  html-escaper@2.0.2:
    resolution:
      {
        integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==,
      }

  http-cache-semantics@4.1.1:
    resolution:
      {
        integrity: sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==,
      }

  http-errors@2.0.0:
    resolution:
      {
        integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==,
      }
    engines: { node: ">= 0.8" }

  http-proxy-agent@4.0.1:
    resolution:
      {
        integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==,
      }
    engines: { node: ">= 6" }

  https-proxy-agent@5.0.1:
    resolution:
      {
        integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==,
      }
    engines: { node: ">= 6" }

  https-proxy-agent@7.0.6:
    resolution:
      {
        integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==,
      }
    engines: { node: ">= 14" }

  human-signals@2.1.0:
    resolution:
      {
        integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==,
      }
    engines: { node: ">=10.17.0" }

  humanize-ms@1.2.1:
    resolution:
      {
        integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==,
      }

  iconv-lite@0.6.3:
    resolution:
      {
        integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==,
      }
    engines: { node: ">=0.10.0" }

  ieee754@1.2.1:
    resolution:
      {
        integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==,
      }

  ignore-by-default@1.0.1:
    resolution:
      {
        integrity: sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==,
      }

  import-local@3.2.0:
    resolution:
      {
        integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==,
      }
    engines: { node: ">=8" }
    hasBin: true

  imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
      }
    engines: { node: ">=0.8.19" }

  indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
      }
    engines: { node: ">=8" }

  infer-owner@1.0.4:
    resolution:
      {
        integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==,
      }

  inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==,
      }
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
      }

  ini@1.3.8:
    resolution:
      {
        integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==,
      }

  ip-address@9.0.5:
    resolution:
      {
        integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==,
      }
    engines: { node: ">= 12" }

  ipaddr.js@1.9.1:
    resolution:
      {
        integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==,
      }
    engines: { node: ">= 0.10" }

  is-arrayish@0.2.1:
    resolution:
      {
        integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==,
      }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
      }
    engines: { node: ">=8" }

  is-buffer@1.1.6:
    resolution:
      {
        integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==,
      }

  is-core-module@2.16.1:
    resolution:
      {
        integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==,
      }
    engines: { node: ">= 0.4" }

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
      }
    engines: { node: ">=0.10.0" }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
      }
    engines: { node: ">=8" }

  is-generator-fn@2.1.0:
    resolution:
      {
        integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==,
      }
    engines: { node: ">=6" }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
      }
    engines: { node: ">=0.10.0" }

  is-lambda@1.0.1:
    resolution:
      {
        integrity: sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==,
      }

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
      }
    engines: { node: ">=0.12.0" }

  is-promise@4.0.0:
    resolution:
      {
        integrity: sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==,
      }

  is-stream@2.0.1:
    resolution:
      {
        integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==,
      }
    engines: { node: ">=8" }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
      }

  istanbul-lib-coverage@3.2.2:
    resolution:
      {
        integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-instrument@5.2.1:
    resolution:
      {
        integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-instrument@6.0.3:
    resolution:
      {
        integrity: sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==,
      }
    engines: { node: ">=10" }

  istanbul-lib-report@3.0.1:
    resolution:
      {
        integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==,
      }
    engines: { node: ">=10" }

  istanbul-lib-source-maps@4.0.1:
    resolution:
      {
        integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==,
      }
    engines: { node: ">=10" }

  istanbul-reports@3.1.7:
    resolution:
      {
        integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==,
      }
    engines: { node: ">=8" }

  jackspeak@3.4.3:
    resolution:
      {
        integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
      }

  jake@10.9.2:
    resolution:
      {
        integrity: sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==,
      }
    engines: { node: ">=10" }
    hasBin: true

  jest-changed-files@29.7.0:
    resolution:
      {
        integrity: sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-circus@29.7.0:
    resolution:
      {
        integrity: sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-cli@29.7.0:
    resolution:
      {
        integrity: sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution:
      {
        integrity: sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@types/node": "*"
      ts-node: ">=9.0.0"
    peerDependenciesMeta:
      "@types/node":
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution:
      {
        integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-docblock@29.7.0:
    resolution:
      {
        integrity: sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-each@29.7.0:
    resolution:
      {
        integrity: sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-environment-node@29.7.0:
    resolution:
      {
        integrity: sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-get-type@29.6.3:
    resolution:
      {
        integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-haste-map@29.7.0:
    resolution:
      {
        integrity: sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-leak-detector@29.7.0:
    resolution:
      {
        integrity: sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-matcher-utils@29.7.0:
    resolution:
      {
        integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-message-util@29.7.0:
    resolution:
      {
        integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-mock@29.7.0:
    resolution:
      {
        integrity: sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-pnp-resolver@1.2.3:
    resolution:
      {
        integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==,
      }
    engines: { node: ">=6" }
    peerDependencies:
      jest-resolve: "*"
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution:
      {
        integrity: sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-resolve-dependencies@29.7.0:
    resolution:
      {
        integrity: sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-resolve@29.7.0:
    resolution:
      {
        integrity: sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-runner@29.7.0:
    resolution:
      {
        integrity: sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-runtime@29.7.0:
    resolution:
      {
        integrity: sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-snapshot@29.7.0:
    resolution:
      {
        integrity: sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-util@29.7.0:
    resolution:
      {
        integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-validate@29.7.0:
    resolution:
      {
        integrity: sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-watcher@29.7.0:
    resolution:
      {
        integrity: sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-worker@29.7.0:
    resolution:
      {
        integrity: sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest@29.7.0:
    resolution:
      {
        integrity: sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  joycon@3.1.1:
    resolution:
      {
        integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==,
      }
    engines: { node: ">=10" }

  js-tiktoken@1.0.19:
    resolution:
      {
        integrity: sha512-XC63YQeEcS47Y53gg950xiZ4IWmkfMe4p2V9OSaBt26q+p47WHn18izuXzSclCI73B7yGqtfRsT6jcZQI0y08g==,
      }

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
      }

  js-yaml@3.14.1:
    resolution:
      {
        integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==,
      }
    hasBin: true

  jsbn@1.1.0:
    resolution:
      {
        integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==,
      }

  jsesc@3.1.0:
    resolution:
      {
        integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==,
      }
    engines: { node: ">=6" }
    hasBin: true

  json-bigint@1.0.0:
    resolution:
      {
        integrity: sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==,
      }

  json-parse-even-better-errors@2.3.1:
    resolution:
      {
        integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==,
      }

  json-parse-even-better-errors@3.0.2:
    resolution:
      {
        integrity: sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==,
      }
    engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

  json-schema-traverse@0.4.1:
    resolution:
      {
        integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==,
      }

  json5@2.2.3:
    resolution:
      {
        integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
      }
    engines: { node: ">=6" }
    hasBin: true

  jwa@2.0.0:
    resolution:
      {
        integrity: sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==,
      }

  jws@4.0.0:
    resolution:
      {
        integrity: sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==,
      }

  kleur@3.0.3:
    resolution:
      {
        integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==,
      }
    engines: { node: ">=6" }

  kolorist@1.8.0:
    resolution:
      {
        integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==,
      }

  langsmith@0.3.15:
    resolution:
      {
        integrity: sha512-cv3ebg0Hh0gRbl72cv/uzaZ+KOdfa2mGF1s74vmB2vlNVO/Ap/O9RYaHV+tpR8nwhGZ50R3ILnTOwSwGP+XQxw==,
      }
    peerDependencies:
      openai: "*"
    peerDependenciesMeta:
      openai:
        optional: true

  leven@3.1.0:
    resolution:
      {
        integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==,
      }
    engines: { node: ">=6" }

  lilconfig@3.1.3:
    resolution:
      {
        integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
      }
    engines: { node: ">=14" }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
      }

  lines-and-columns@2.0.4:
    resolution:
      {
        integrity: sha512-wM1+Z03eypVAVUCE7QdSqpVIvelbOakn1M0bPDoA4SGWPx3sNDVUiMo3L6To6WWGClB7VyXnhQ4Sn7gxiJbE6A==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  load-tsconfig@0.2.5:
    resolution:
      {
        integrity: sha512-IXO6OCs9yg8tMKzfPZ1YmheJbZCiEsnBdcB03l0OcfK9prKnJb96siuHCr5Fl37/yo9DnKU+TLpxzTUspw9shg==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==,
      }
    engines: { node: ">=8" }

  lodash.memoize@4.1.2:
    resolution:
      {
        integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==,
      }

  lodash.sortby@4.7.0:
    resolution:
      {
        integrity: sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==,
      }

  lru-cache@10.4.3:
    resolution:
      {
        integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
      }

  lru-cache@5.1.1:
    resolution:
      {
        integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
      }

  lru-cache@6.0.0:
    resolution:
      {
        integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==,
      }
    engines: { node: ">=10" }

  make-dir@4.0.0:
    resolution:
      {
        integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==,
      }
    engines: { node: ">=10" }

  make-error@1.3.6:
    resolution:
      {
        integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==,
      }

  make-fetch-happen@9.1.0:
    resolution:
      {
        integrity: sha512-+zopwDy7DNknmwPQplem5lAZX/eCOzSvSNNcSKm5eVwTkOBzoktEfXsa9L23J/GIRhxRsaxzkPEhrJEpE2F4Gg==,
      }
    engines: { node: ">= 10" }

  makeerror@1.0.12:
    resolution:
      {
        integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==,
      }

  math-intrinsics@1.1.0:
    resolution:
      {
        integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==,
      }
    engines: { node: ">= 0.4" }

  md5@2.3.0:
    resolution:
      {
        integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==,
      }

  media-typer@1.1.0:
    resolution:
      {
        integrity: sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==,
      }
    engines: { node: ">= 0.8" }

  merge-descriptors@2.0.0:
    resolution:
      {
        integrity: sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==,
      }
    engines: { node: ">=18" }

  merge-stream@2.0.0:
    resolution:
      {
        integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
      }
    engines: { node: ">= 8" }

  micromatch@4.0.8:
    resolution:
      {
        integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==,
      }
    engines: { node: ">=8.6" }

  mime-db@1.52.0:
    resolution:
      {
        integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
      }
    engines: { node: ">= 0.6" }

  mime-db@1.54.0:
    resolution:
      {
        integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==,
      }
    engines: { node: ">= 0.6" }

  mime-types@2.1.35:
    resolution:
      {
        integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
      }
    engines: { node: ">= 0.6" }

  mime-types@3.0.1:
    resolution:
      {
        integrity: sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==,
      }
    engines: { node: ">= 0.6" }

  mimic-fn@2.1.0:
    resolution:
      {
        integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==,
      }
    engines: { node: ">=6" }

  mimic-response@3.1.0:
    resolution:
      {
        integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==,
      }
    engines: { node: ">=10" }

  minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
      }

  minimatch@5.1.6:
    resolution:
      {
        integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==,
      }
    engines: { node: ">=10" }

  minimatch@9.0.5:
    resolution:
      {
        integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
      }

  minipass-collect@1.0.2:
    resolution:
      {
        integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==,
      }
    engines: { node: ">= 8" }

  minipass-fetch@1.4.1:
    resolution:
      {
        integrity: sha512-CGH1eblLq26Y15+Azk7ey4xh0J/XfJfrCox5LDJiKqI2Q2iwOLOKrlmIaODiSQS8d18jalF6y2K2ePUm0CmShw==,
      }
    engines: { node: ">=8" }

  minipass-flush@1.0.5:
    resolution:
      {
        integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==,
      }
    engines: { node: ">= 8" }

  minipass-pipeline@1.2.4:
    resolution:
      {
        integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==,
      }
    engines: { node: ">=8" }

  minipass-sized@1.0.3:
    resolution:
      {
        integrity: sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==,
      }
    engines: { node: ">=8" }

  minipass@3.3.6:
    resolution:
      {
        integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==,
      }
    engines: { node: ">=8" }

  minipass@5.0.0:
    resolution:
      {
        integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==,
      }
    engines: { node: ">=8" }

  minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minizlib@2.1.2:
    resolution:
      {
        integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==,
      }
    engines: { node: ">= 8" }

  mkdirp-classic@0.5.3:
    resolution:
      {
        integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==,
      }

  mkdirp@1.0.4:
    resolution:
      {
        integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==,
      }
    engines: { node: ">=10" }
    hasBin: true

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
      }

  mustache@4.2.0:
    resolution:
      {
        integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==,
      }
    hasBin: true

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
      }

  nanoid@3.3.8:
    resolution:
      {
        integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  napi-build-utils@2.0.0:
    resolution:
      {
        integrity: sha512-GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA==,
      }

  natural-compare@1.4.0:
    resolution:
      {
        integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
      }

  negotiator@0.6.4:
    resolution:
      {
        integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==,
      }
    engines: { node: ">= 0.6" }

  negotiator@1.0.0:
    resolution:
      {
        integrity: sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==,
      }
    engines: { node: ">= 0.6" }

  neo4j-driver-bolt-connection@5.28.1:
    resolution:
      {
        integrity: sha512-nY8GBhjOW7J0rDtpiyJn6kFdk2OiNVZZhZrO8//mwNXnf5VQJ6HqZQTDthH/9pEaX0Jvbastz1xU7ZL8xzqY0w==,
      }

  neo4j-driver-core@5.28.1:
    resolution:
      {
        integrity: sha512-14vN8TlxC0JvJYfjWic5PwjsZ38loQLOKFTXwk4fWLTbCk6VhrhubB2Jsy9Rz+gM6PtTor4+6ClBEFDp1q/c8g==,
      }

  neo4j-driver@5.28.1:
    resolution:
      {
        integrity: sha512-jbyBwyM0a3RLGcP43q3hIxPUPxA+1bE04RovOKdNAS42EtBMVCKcPSeOvWiHxgXp1ZFd0a8XqK+7LtguInOLUg==,
      }

  node-abi@3.74.0:
    resolution:
      {
        integrity: sha512-c5XK0MjkGBrQPGYG24GBADZud0NCbznxNx0ZkS+ebUTrmV1qTDxPxSL8zEAPURXSbLRWVexxmP4986BziahL5w==,
      }
    engines: { node: ">=10" }

  node-addon-api@7.1.1:
    resolution:
      {
        integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==,
      }

  node-domexception@1.0.0:
    resolution:
      {
        integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==,
      }
    engines: { node: ">=10.5.0" }

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-gyp@8.4.1:
    resolution:
      {
        integrity: sha512-olTJRgUtAb/hOXG0E93wZDs5YiJlgbXxTwQAFHyNlRsXQnYzUaF2aGgujZbw+hR8aF4ZG/rST57bWMWD16jr9w==,
      }
    engines: { node: ">= 10.12.0" }
    hasBin: true

  node-int64@0.4.0:
    resolution:
      {
        integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==,
      }

  node-releases@2.0.19:
    resolution:
      {
        integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==,
      }

  nodemon@3.1.9:
    resolution:
      {
        integrity: sha512-hdr1oIb2p6ZSxu3PB2JWWYS7ZQ0qvaZsc3hK8DR8f02kRzc8rjYmxAIvdz+aYC+8F2IjNaB7HMcSDg8nQpJxyg==,
      }
    engines: { node: ">=10" }
    hasBin: true

  nopt@5.0.0:
    resolution:
      {
        integrity: sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==,
      }
    engines: { node: ">=6" }
    hasBin: true

  normalize-package-data@6.0.2:
    resolution:
      {
        integrity: sha512-V6gygoYb/5EmNI+MEGrWkC+e6+Rr7mTmfHrxDbLzxQogBkgzo76rkok0Am6thgSF7Mv2nLOajAJj5vDJZEFn7g==,
      }
    engines: { node: ^16.14.0 || >=18.0.0 }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
      }
    engines: { node: ">=0.10.0" }

  npm-run-path@4.0.1:
    resolution:
      {
        integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==,
      }
    engines: { node: ">=8" }

  npmlog@6.0.2:
    resolution:
      {
        integrity: sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
      }
    engines: { node: ">=0.10.0" }

  object-inspect@1.13.4:
    resolution:
      {
        integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==,
      }
    engines: { node: ">= 0.4" }

  obuf@1.1.2:
    resolution:
      {
        integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==,
      }

  ollama@0.5.14:
    resolution:
      {
        integrity: sha512-pvOuEYa2WkkAumxzJP0RdEYHkbZ64AYyyUszXVX7ruLvk5L+EiO2G71da2GqEQ4IAk4j6eLoUbGk5arzFT1wJA==,
      }

  on-finished@2.4.1:
    resolution:
      {
        integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==,
      }
    engines: { node: ">= 0.8" }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
      }

  onetime@5.1.2:
    resolution:
      {
        integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==,
      }
    engines: { node: ">=6" }

  openai@4.93.0:
    resolution:
      {
        integrity: sha512-2kONcISbThKLfm7T9paVzg+QCE1FOZtNMMUfXyXckUAoXRRS/mTP89JSDHPMp8uM5s0bz28RISbvQjArD6mgUQ==,
      }
    hasBin: true
    peerDependencies:
      ws: ^8.18.0
      zod: ^3.23.8
    peerDependenciesMeta:
      ws:
        optional: true
      zod:
        optional: true

  p-finally@1.0.0:
    resolution:
      {
        integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==,
      }
    engines: { node: ">=4" }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==,
      }
    engines: { node: ">=6" }

  p-limit@3.1.0:
    resolution:
      {
        integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
      }
    engines: { node: ">=10" }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==,
      }
    engines: { node: ">=8" }

  p-map@4.0.0:
    resolution:
      {
        integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==,
      }
    engines: { node: ">=10" }

  p-queue@6.6.2:
    resolution:
      {
        integrity: sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==,
      }
    engines: { node: ">=8" }

  p-retry@4.6.2:
    resolution:
      {
        integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==,
      }
    engines: { node: ">=8" }

  p-timeout@3.2.0:
    resolution:
      {
        integrity: sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==,
      }
    engines: { node: ">=8" }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==,
      }
    engines: { node: ">=6" }

  package-json-from-dist@1.0.1:
    resolution:
      {
        integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
      }

  packet-reader@1.0.0:
    resolution:
      {
        integrity: sha512-HAKu/fG3HpHFO0AA8WE8q2g+gBJaZ9MG7fcKk+IJPLTGAD6Psw4443l+9DGRbOIh3/aXr7Phy0TjilYivJo5XQ==,
      }

  parse-json@5.2.0:
    resolution:
      {
        integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==,
      }
    engines: { node: ">=8" }

  parse-json@7.1.1:
    resolution:
      {
        integrity: sha512-SgOTCX/EZXtZxBE5eJ97P4yGM5n37BwRU+YMsH4vNzFqJV/oWFXXCmwFlgWUM4PrakybVOueJJ6pwHqSVhTFDw==,
      }
    engines: { node: ">=16" }

  parseurl@1.3.3:
    resolution:
      {
        integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==,
      }
    engines: { node: ">= 0.8" }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
      }
    engines: { node: ">=8" }

  path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==,
      }
    engines: { node: ">=0.10.0" }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
      }
    engines: { node: ">=8" }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
      }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
      }
    engines: { node: ">=16 || 14 >=14.18" }

  path-to-regexp@8.2.0:
    resolution:
      {
        integrity: sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==,
      }
    engines: { node: ">=16" }

  pg-cloudflare@1.1.1:
    resolution:
      {
        integrity: sha512-xWPagP/4B6BgFO+EKz3JONXv3YDgvkbVrGw2mTo3D6tVDQRh1e7cqVGvyR3BE+eQgAvx1XhW/iEASj4/jCWl3Q==,
      }

  pg-connection-string@2.7.0:
    resolution:
      {
        integrity: sha512-PI2W9mv53rXJQEOb8xNR8lH7Hr+EKa6oJa38zsK0S/ky2er16ios1wLKhZyxzD7jUReiWokc9WK5nxSnC7W1TA==,
      }

  pg-int8@1.0.1:
    resolution:
      {
        integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==,
      }
    engines: { node: ">=4.0.0" }

  pg-numeric@1.0.2:
    resolution:
      {
        integrity: sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==,
      }
    engines: { node: ">=4" }

  pg-pool@3.7.1:
    resolution:
      {
        integrity: sha512-xIOsFoh7Vdhojas6q3596mXFsR8nwBQBXX5JiV7p9buEVAGqYL4yFzclON5P9vFrpu1u7Zwl2oriyDa89n0wbw==,
      }
    peerDependencies:
      pg: ">=8.0"

  pg-protocol@1.7.1:
    resolution:
      {
        integrity: sha512-gjTHWGYWsEgy9MsY0Gp6ZJxV24IjDqdpTW7Eh0x+WfJLFsm/TJx1MzL6T0D88mBvkpxotCQ6TwW6N+Kko7lhgQ==,
      }

  pg-types@2.2.0:
    resolution:
      {
        integrity: sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==,
      }
    engines: { node: ">=4" }

  pg-types@4.0.2:
    resolution:
      {
        integrity: sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==,
      }
    engines: { node: ">=10" }

  pg@8.11.3:
    resolution:
      {
        integrity: sha512-+9iuvG8QfaaUrrph+kpF24cXkH1YOOUeArRNYIxq1viYHZagBxrTno7cecY1Fa44tJeZvaoG+Djpkc3JwehN5g==,
      }
    engines: { node: ">= 8.0.0" }
    peerDependencies:
      pg-native: ">=3.0.1"
    peerDependenciesMeta:
      pg-native:
        optional: true

  pgpass@1.0.5:
    resolution:
      {
        integrity: sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==,
      }

  picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
      }
    engines: { node: ">=8.6" }

  picomatch@4.0.2:
    resolution:
      {
        integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==,
      }
    engines: { node: ">=12" }

  pirates@4.0.6:
    resolution:
      {
        integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
      }
    engines: { node: ">= 6" }

  pkce-challenge@5.0.0:
    resolution:
      {
        integrity: sha512-ueGLflrrnvwB3xuo/uGob5pd5FN7l0MsLf0Z87o/UQmRtwjvfylfc9MurIxRAWywCYTgrvpXBcqjV4OfCYGCIQ==,
      }
    engines: { node: ">=16.20.0" }

  pkg-dir@4.2.0:
    resolution:
      {
        integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==,
      }
    engines: { node: ">=8" }

  postcss-load-config@6.0.1:
    resolution:
      {
        integrity: sha512-oPtTM4oerL+UXmx+93ytZVN82RrlY/wPUV8IeDxFrzIjXOLF1pN+EmKPLbubvKHT2HC20xXsCAH2Z+CKV6Oz/g==,
      }
    engines: { node: ">= 18" }
    peerDependencies:
      jiti: ">=1.21.0"
      postcss: ">=8.0.9"
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      jiti:
        optional: true
      postcss:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  postcss@8.5.3:
    resolution:
      {
        integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==,
      }
    engines: { node: ^10 || ^12 || >=14 }

  postgres-array@2.0.0:
    resolution:
      {
        integrity: sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==,
      }
    engines: { node: ">=4" }

  postgres-array@3.0.2:
    resolution:
      {
        integrity: sha512-6faShkdFugNQCLwucjPcY5ARoW1SlbnrZjmGl0IrrqewpvxvhSLHimCVzqeuULCbG0fQv7Dtk1yDbG3xv7Veog==,
      }
    engines: { node: ">=12" }

  postgres-bytea@1.0.0:
    resolution:
      {
        integrity: sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==,
      }
    engines: { node: ">=0.10.0" }

  postgres-bytea@3.0.0:
    resolution:
      {
        integrity: sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==,
      }
    engines: { node: ">= 6" }

  postgres-date@1.0.7:
    resolution:
      {
        integrity: sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==,
      }
    engines: { node: ">=0.10.0" }

  postgres-date@2.1.0:
    resolution:
      {
        integrity: sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==,
      }
    engines: { node: ">=12" }

  postgres-interval@1.2.0:
    resolution:
      {
        integrity: sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==,
      }
    engines: { node: ">=0.10.0" }

  postgres-interval@3.0.0:
    resolution:
      {
        integrity: sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==,
      }
    engines: { node: ">=12" }

  postgres-range@1.1.4:
    resolution:
      {
        integrity: sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==,
      }

  prebuild-install@7.1.3:
    resolution:
      {
        integrity: sha512-8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug==,
      }
    engines: { node: ">=10" }
    hasBin: true

  prettier@3.5.2:
    resolution:
      {
        integrity: sha512-lc6npv5PH7hVqozBR7lkBNOGXV9vMwROAPlumdBkX0wTbbzPu/U1hk5yL8p2pt4Xoc+2mkT8t/sow2YrV/M5qg==,
      }
    engines: { node: ">=14" }
    hasBin: true

  pretty-format@29.7.0:
    resolution:
      {
        integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  promise-inflight@1.0.1:
    resolution:
      {
        integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==,
      }
    peerDependencies:
      bluebird: "*"
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution:
      {
        integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==,
      }
    engines: { node: ">=10" }

  prompts@2.4.2:
    resolution:
      {
        integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==,
      }
    engines: { node: ">= 6" }

  proxy-addr@2.0.7:
    resolution:
      {
        integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==,
      }
    engines: { node: ">= 0.10" }

  proxy-from-env@1.1.0:
    resolution:
      {
        integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==,
      }

  pstree.remy@1.1.8:
    resolution:
      {
        integrity: sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==,
      }

  pump@3.0.2:
    resolution:
      {
        integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==,
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
      }
    engines: { node: ">=6" }

  pure-rand@6.1.0:
    resolution:
      {
        integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==,
      }

  qs@6.14.0:
    resolution:
      {
        integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==,
      }
    engines: { node: ">=0.6" }

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
      }

  range-parser@1.2.1:
    resolution:
      {
        integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==,
      }
    engines: { node: ">= 0.6" }

  raw-body@3.0.0:
    resolution:
      {
        integrity: sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==,
      }
    engines: { node: ">= 0.8" }

  rc@1.2.8:
    resolution:
      {
        integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==,
      }
    hasBin: true

  react-is@18.3.1:
    resolution:
      {
        integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==,
      }

  read-pkg@8.1.0:
    resolution:
      {
        integrity: sha512-PORM8AgzXeskHO/WEv312k9U03B8K9JSiWF/8N9sUuFjBa+9SF2u6K7VClzXwDXab51jCd8Nd36CNM+zR97ScQ==,
      }
    engines: { node: ">=16" }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
      }
    engines: { node: ">= 6" }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
      }
    engines: { node: ">=8.10.0" }

  readdirp@4.1.2:
    resolution:
      {
        integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==,
      }
    engines: { node: ">= 14.18.0" }

  redis@4.7.0:
    resolution:
      {
        integrity: sha512-zvmkHEAdGMn+hMRXuMBtu4Vo5P6rHQjLoHftu+lBqq8ZTA3RCVC/WzD790bkKKiNFp7d5/9PcSD19fJyyRvOdQ==,
      }

  require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
      }
    engines: { node: ">=0.10.0" }

  resolve-cwd@3.0.0:
    resolution:
      {
        integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==,
      }
    engines: { node: ">=8" }

  resolve-from@5.0.0:
    resolution:
      {
        integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==,
      }
    engines: { node: ">=8" }

  resolve.exports@2.0.3:
    resolution:
      {
        integrity: sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==,
      }
    engines: { node: ">=10" }

  resolve@1.22.10:
    resolution:
      {
        integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==,
      }
    engines: { node: ">= 0.4" }
    hasBin: true

  retry@0.12.0:
    resolution:
      {
        integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==,
      }
    engines: { node: ">= 4" }

  retry@0.13.1:
    resolution:
      {
        integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==,
      }
    engines: { node: ">= 4" }

  reusify@1.1.0:
    resolution:
      {
        integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==,
      }
    engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

  rimraf@3.0.2:
    resolution:
      {
        integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==,
      }
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@5.0.10:
    resolution:
      {
        integrity: sha512-l0OE8wL34P4nJH/H2ffoaniAokM2qSmrtXHmlpvYr5AVVX8msAyW0l8NVJFDxlSK4u3Uh/f41cQheDVdnYijwQ==,
      }
    hasBin: true

  rollup@4.37.0:
    resolution:
      {
        integrity: sha512-iAtQy/L4QFU+rTJ1YUjXqJOJzuwEghqWzCEYD2FEghT7Gsy1VdABntrO4CLopA5IkflTyqNiLNwPcOJ3S7UKLg==,
      }
    engines: { node: ">=18.0.0", npm: ">=8.0.0" }
    hasBin: true

  router@2.2.0:
    resolution:
      {
        integrity: sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==,
      }
    engines: { node: ">= 18" }

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
      }

  rxjs@7.8.2:
    resolution:
      {
        integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==,
      }

  safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
      }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
      }

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
      }
    hasBin: true

  semver@7.7.1:
    resolution:
      {
        integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==,
      }
    engines: { node: ">=10" }
    hasBin: true

  send@1.2.0:
    resolution:
      {
        integrity: sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==,
      }
    engines: { node: ">= 18" }

  serve-static@2.2.0:
    resolution:
      {
        integrity: sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==,
      }
    engines: { node: ">= 18" }

  set-blocking@2.0.0:
    resolution:
      {
        integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==,
      }

  setprototypeof@1.2.0:
    resolution:
      {
        integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==,
      }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
      }
    engines: { node: ">=8" }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
      }
    engines: { node: ">=8" }

  side-channel-list@1.0.0:
    resolution:
      {
        integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==,
      }
    engines: { node: ">= 0.4" }

  side-channel-map@1.0.1:
    resolution:
      {
        integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==,
      }
    engines: { node: ">= 0.4" }

  side-channel-weakmap@1.0.2:
    resolution:
      {
        integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==,
      }
    engines: { node: ">= 0.4" }

  side-channel@1.1.0:
    resolution:
      {
        integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==,
      }
    engines: { node: ">= 0.4" }

  signal-exit@3.0.7:
    resolution:
      {
        integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
      }
    engines: { node: ">=14" }

  simple-concat@1.0.1:
    resolution:
      {
        integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==,
      }

  simple-get@4.0.1:
    resolution:
      {
        integrity: sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==,
      }

  simple-update-notifier@2.0.0:
    resolution:
      {
        integrity: sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==,
      }
    engines: { node: ">=10" }

  simple-wcswidth@1.0.1:
    resolution:
      {
        integrity: sha512-xMO/8eNREtaROt7tJvWJqHBDTMFN4eiQ5I4JRMuilwfnFcV5W9u7RUkueNkdw0jPqGMX36iCywelS5yilTuOxg==,
      }

  sisteransi@1.0.5:
    resolution:
      {
        integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==,
      }

  slash@3.0.0:
    resolution:
      {
        integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==,
      }
    engines: { node: ">=8" }

  smart-buffer@4.2.0:
    resolution:
      {
        integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==,
      }
    engines: { node: ">= 6.0.0", npm: ">= 3.0.0" }

  socks-proxy-agent@6.2.1:
    resolution:
      {
        integrity: sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==,
      }
    engines: { node: ">= 10" }

  socks@2.8.4:
    resolution:
      {
        integrity: sha512-D3YaD0aRxR3mEcqnidIs7ReYJFVzWdd6fXJYUM8ixcQcJRGTka/b3saV0KflYhyVJXKhb947GndU35SxYNResQ==,
      }
    engines: { node: ">= 10.0.0", npm: ">= 3.0.0" }

  source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
      }
    engines: { node: ">=0.10.0" }

  source-map-support@0.5.13:
    resolution:
      {
        integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==,
      }

  source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
      }
    engines: { node: ">=0.10.0" }

  source-map@0.8.0-beta.0:
    resolution:
      {
        integrity: sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==,
      }
    engines: { node: ">= 8" }

  spdx-correct@3.2.0:
    resolution:
      {
        integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==,
      }

  spdx-exceptions@2.5.0:
    resolution:
      {
        integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==,
      }

  spdx-expression-parse@3.0.1:
    resolution:
      {
        integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==,
      }

  spdx-license-ids@3.0.21:
    resolution:
      {
        integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==,
      }

  split2@4.2.0:
    resolution:
      {
        integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==,
      }
    engines: { node: ">= 10.x" }

  sprintf-js@1.0.3:
    resolution:
      {
        integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==,
      }

  sprintf-js@1.1.3:
    resolution:
      {
        integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==,
      }

  sqlite3@5.1.7:
    resolution:
      {
        integrity: sha512-GGIyOiFaG+TUra3JIfkI/zGP8yZYLPQ0pl1bH+ODjiX57sPhrLU5sQJn1y9bDKZUFYkX1crlrPfSYt0BKKdkog==,
      }

  ssri@8.0.1:
    resolution:
      {
        integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==,
      }
    engines: { node: ">= 8" }

  stack-utils@2.0.6:
    resolution:
      {
        integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==,
      }
    engines: { node: ">=10" }

  statuses@2.0.1:
    resolution:
      {
        integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==,
      }
    engines: { node: ">= 0.8" }

  string-length@4.0.2:
    resolution:
      {
        integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==,
      }
    engines: { node: ">=10" }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
      }
    engines: { node: ">=8" }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
      }
    engines: { node: ">=12" }

  string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
      }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
      }
    engines: { node: ">=8" }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
      }
    engines: { node: ">=12" }

  strip-bom@4.0.0:
    resolution:
      {
        integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==,
      }
    engines: { node: ">=8" }

  strip-final-newline@2.0.0:
    resolution:
      {
        integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==,
      }
    engines: { node: ">=6" }

  strip-json-comments@2.0.1:
    resolution:
      {
        integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==,
      }
    engines: { node: ">=0.10.0" }

  strip-json-comments@3.1.1:
    resolution:
      {
        integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
      }
    engines: { node: ">=8" }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    hasBin: true

  supports-color@5.5.0:
    resolution:
      {
        integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==,
      }
    engines: { node: ">=4" }

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
      }
    engines: { node: ">=8" }

  supports-color@8.1.1:
    resolution:
      {
        integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==,
      }
    engines: { node: ">=10" }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
      }
    engines: { node: ">= 0.4" }

  tar-fs@2.1.2:
    resolution:
      {
        integrity: sha512-EsaAXwxmx8UB7FRKqeozqEPop69DXcmYwTQwXvyAPF352HJsPdkVhvTaDPYqfNgruveJIJy3TA2l+2zj8LJIJA==,
      }

  tar-stream@2.2.0:
    resolution:
      {
        integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==,
      }
    engines: { node: ">=6" }

  tar@6.2.1:
    resolution:
      {
        integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==,
      }
    engines: { node: ">=10" }

  test-exclude@6.0.0:
    resolution:
      {
        integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==,
      }
    engines: { node: ">=8" }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
      }
    engines: { node: ">=0.8" }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
      }

  tinyexec@0.3.2:
    resolution:
      {
        integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==,
      }

  tinyglobby@0.2.12:
    resolution:
      {
        integrity: sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==,
      }
    engines: { node: ">=12.0.0" }

  tmpl@1.0.5:
    resolution:
      {
        integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==,
      }

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
      }
    engines: { node: ">=8.0" }

  toidentifier@1.0.1:
    resolution:
      {
        integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==,
      }
    engines: { node: ">=0.6" }

  touch@3.1.1:
    resolution:
      {
        integrity: sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==,
      }
    hasBin: true

  tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==,
      }

  tr46@1.0.1:
    resolution:
      {
        integrity: sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==,
      }

  tree-kill@1.2.2:
    resolution:
      {
        integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==,
      }
    hasBin: true

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
      }

  ts-jest@29.2.6:
    resolution:
      {
        integrity: sha512-yTNZVZqc8lSixm+QGVFcPe6+yj7+TWZwIesuOWvfcn4B9bz5x4NDzVCQQjOs7Hfouu36aEqfEbo9Qpo+gq8dDg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0 }
    hasBin: true
    peerDependencies:
      "@babel/core": ">=7.0.0-beta.0 <8"
      "@jest/transform": ^29.0.0
      "@jest/types": ^29.0.0
      babel-jest: ^29.0.0
      esbuild: "*"
      jest: ^29.0.0
      typescript: ">=4.3 <6"
    peerDependenciesMeta:
      "@babel/core":
        optional: true
      "@jest/transform":
        optional: true
      "@jest/types":
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true

  ts-node@10.9.2:
    resolution:
      {
        integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==,
      }
    hasBin: true
    peerDependencies:
      "@swc/core": ">=1.2.50"
      "@swc/wasm": ">=1.2.50"
      "@types/node": "*"
      typescript: ">=2.7"
    peerDependenciesMeta:
      "@swc/core":
        optional: true
      "@swc/wasm":
        optional: true

  tslib@2.8.1:
    resolution:
      {
        integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==,
      }

  tsup@8.4.0:
    resolution:
      {
        integrity: sha512-b+eZbPCjz10fRryaAA7C8xlIHnf8VnsaRqydheLIqwG/Mcpfk8Z5zp3HayX7GaTygkigHl5cBUs+IhcySiIexQ==,
      }
    engines: { node: ">=18" }
    hasBin: true
    peerDependencies:
      "@microsoft/api-extractor": ^7.36.0
      "@swc/core": ^1
      postcss: ^8.4.12
      typescript: ">=4.5.0"
    peerDependenciesMeta:
      "@microsoft/api-extractor":
        optional: true
      "@swc/core":
        optional: true
      postcss:
        optional: true
      typescript:
        optional: true

  tunnel-agent@0.6.0:
    resolution:
      {
        integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==,
      }

  type-detect@4.0.8:
    resolution:
      {
        integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==,
      }
    engines: { node: ">=4" }

  type-fest@0.21.3:
    resolution:
      {
        integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==,
      }
    engines: { node: ">=10" }

  type-fest@3.13.1:
    resolution:
      {
        integrity: sha512-tLq3bSNx+xSpwvAJnzrK0Ep5CLNWjvFTOp71URMaAEWBfRb9nnJiBoUe0tF8bI4ZFO3omgBR6NvnbzVUT3Ly4g==,
      }
    engines: { node: ">=14.16" }

  type-fest@4.35.0:
    resolution:
      {
        integrity: sha512-2/AwEFQDFEy30iOLjrvHDIH7e4HEWH+f1Yl1bI5XMqzuoCUqwYCdxachgsgv0og/JdVZUhbfjcJAoHj5L1753A==,
      }
    engines: { node: ">=16" }

  type-is@2.0.1:
    resolution:
      {
        integrity: sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==,
      }
    engines: { node: ">= 0.6" }

  typescript@5.5.4:
    resolution:
      {
        integrity: sha512-Mtq29sKDAEYP7aljRgtPOpTvOfbwRWlS6dPRzwjdE+C0R4brX/GUyhHSecbHMFLNBLcJIPt9nl9yG5TZ1weH+Q==,
      }
    engines: { node: ">=14.17" }
    hasBin: true

  undefsafe@2.0.5:
    resolution:
      {
        integrity: sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==,
      }

  undici-types@5.26.5:
    resolution:
      {
        integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==,
      }

  undici-types@6.20.0:
    resolution:
      {
        integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==,
      }

  undici@5.28.5:
    resolution:
      {
        integrity: sha512-zICwjrDrcrUE0pyyJc1I2QzBkLM8FINsgOrt6WjA+BgajVq9Nxu2PbFFXUrAggLfDXlZGZBVZYw7WNV5KiBiBA==,
      }
    engines: { node: ">=14.0" }

  unique-filename@1.1.1:
    resolution:
      {
        integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==,
      }

  unique-slug@2.0.2:
    resolution:
      {
        integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==,
      }

  unpipe@1.0.0:
    resolution:
      {
        integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==,
      }
    engines: { node: ">= 0.8" }

  update-browserslist-db@1.1.3:
    resolution:
      {
        integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==,
      }
    hasBin: true
    peerDependencies:
      browserslist: ">= 4.21.0"

  uri-js@4.4.1:
    resolution:
      {
        integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
      }

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
      }

  uuid@10.0.0:
    resolution:
      {
        integrity: sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==,
      }
    hasBin: true

  uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==,
      }
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution:
      {
        integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==,
      }

  v8-to-istanbul@9.3.0:
    resolution:
      {
        integrity: sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==,
      }
    engines: { node: ">=10.12.0" }

  validate-npm-package-license@3.0.4:
    resolution:
      {
        integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==,
      }

  vary@1.1.2:
    resolution:
      {
        integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==,
      }
    engines: { node: ">= 0.8" }

  walker@1.0.8:
    resolution:
      {
        integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==,
      }

  web-streams-polyfill@3.3.3:
    resolution:
      {
        integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==,
      }
    engines: { node: ">= 8" }

  web-streams-polyfill@4.0.0-beta.3:
    resolution:
      {
        integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==,
      }
    engines: { node: ">= 14" }

  webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==,
      }

  webidl-conversions@4.0.2:
    resolution:
      {
        integrity: sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==,
      }

  whatwg-fetch@3.6.20:
    resolution:
      {
        integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==,
      }

  whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==,
      }

  whatwg-url@7.1.0:
    resolution:
      {
        integrity: sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==,
      }

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
      }
    engines: { node: ">= 8" }
    hasBin: true

  wide-align@1.1.5:
    resolution:
      {
        integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==,
      }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
      }
    engines: { node: ">=10" }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
      }
    engines: { node: ">=12" }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
      }

  write-file-atomic@4.0.2:
    resolution:
      {
        integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }

  ws@8.18.1:
    resolution:
      {
        integrity: sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==,
      }
    engines: { node: ">=10.0.0" }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ">=5.0.2"
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xtend@4.0.2:
    resolution:
      {
        integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==,
      }
    engines: { node: ">=0.4" }

  y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
      }
    engines: { node: ">=10" }

  yallist@3.1.1:
    resolution:
      {
        integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
      }

  yallist@4.0.0:
    resolution:
      {
        integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==,
      }

  yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
      }
    engines: { node: ">=12" }

  yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
      }
    engines: { node: ">=12" }

  yn@3.1.1:
    resolution:
      {
        integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==,
      }
    engines: { node: ">=6" }

  yocto-queue@0.1.0:
    resolution:
      {
        integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
      }
    engines: { node: ">=10" }

  zod-to-json-schema@3.24.5:
    resolution:
      {
        integrity: sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==,
      }
    peerDependencies:
      zod: ^3.24.1

  zod@3.24.2:
    resolution:
      {
        integrity: sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==,
      }

snapshots:
  "@ampproject/remapping@2.3.0":
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25

  "@anthropic-ai/sdk@0.40.1(encoding@0.1.13)":
    dependencies:
      "@types/node": 18.19.76
      "@types/node-fetch": 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding

  "@babel/code-frame@7.26.2":
    dependencies:
      "@babel/helper-validator-identifier": 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  "@babel/compat-data@7.26.8": {}

  "@babel/core@7.26.9":
    dependencies:
      "@ampproject/remapping": 2.3.0
      "@babel/code-frame": 7.26.2
      "@babel/generator": 7.26.9
      "@babel/helper-compilation-targets": 7.26.5
      "@babel/helper-module-transforms": 7.26.0(@babel/core@7.26.9)
      "@babel/helpers": 7.26.9
      "@babel/parser": 7.26.9
      "@babel/template": 7.26.9
      "@babel/traverse": 7.26.9
      "@babel/types": 7.26.9
      convert-source-map: 2.0.0
      debug: 4.4.0(supports-color@5.5.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  "@babel/generator@7.26.9":
    dependencies:
      "@babel/parser": 7.26.9
      "@babel/types": 7.26.9
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25
      jsesc: 3.1.0

  "@babel/helper-compilation-targets@7.26.5":
    dependencies:
      "@babel/compat-data": 7.26.8
      "@babel/helper-validator-option": 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  "@babel/helper-module-imports@7.25.9":
    dependencies:
      "@babel/traverse": 7.26.9
      "@babel/types": 7.26.9
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-module-transforms@7.26.0(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-module-imports": 7.25.9
      "@babel/helper-validator-identifier": 7.25.9
      "@babel/traverse": 7.26.9
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-plugin-utils@7.26.5": {}

  "@babel/helper-string-parser@7.25.9": {}

  "@babel/helper-validator-identifier@7.25.9": {}

  "@babel/helper-validator-option@7.25.9": {}

  "@babel/helpers@7.26.9":
    dependencies:
      "@babel/template": 7.26.9
      "@babel/types": 7.26.9

  "@babel/parser@7.26.9":
    dependencies:
      "@babel/types": 7.26.9

  "@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.9)":
    dependencies:
      "@babel/core": 7.26.9
      "@babel/helper-plugin-utils": 7.26.5

  "@babel/template@7.26.9":
    dependencies:
      "@babel/code-frame": 7.26.2
      "@babel/parser": 7.26.9
      "@babel/types": 7.26.9

  "@babel/traverse@7.26.9":
    dependencies:
      "@babel/code-frame": 7.26.2
      "@babel/generator": 7.26.9
      "@babel/parser": 7.26.9
      "@babel/template": 7.26.9
      "@babel/types": 7.26.9
      debug: 4.4.0(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  "@babel/types@7.26.9":
    dependencies:
      "@babel/helper-string-parser": 7.25.9
      "@babel/helper-validator-identifier": 7.25.9

  "@bcoe/v8-coverage@0.2.3": {}

  "@cfworker/json-schema@4.1.1": {}

  "@cspotcode/source-map-support@0.8.1":
    dependencies:
      "@jridgewell/trace-mapping": 0.3.9

  "@esbuild/aix-ppc64@0.25.1":
    optional: true

  "@esbuild/android-arm64@0.25.1":
    optional: true

  "@esbuild/android-arm@0.25.1":
    optional: true

  "@esbuild/android-x64@0.25.1":
    optional: true

  "@esbuild/darwin-arm64@0.25.1":
    optional: true

  "@esbuild/darwin-x64@0.25.1":
    optional: true

  "@esbuild/freebsd-arm64@0.25.1":
    optional: true

  "@esbuild/freebsd-x64@0.25.1":
    optional: true

  "@esbuild/linux-arm64@0.25.1":
    optional: true

  "@esbuild/linux-arm@0.25.1":
    optional: true

  "@esbuild/linux-ia32@0.25.1":
    optional: true

  "@esbuild/linux-loong64@0.25.1":
    optional: true

  "@esbuild/linux-mips64el@0.25.1":
    optional: true

  "@esbuild/linux-ppc64@0.25.1":
    optional: true

  "@esbuild/linux-riscv64@0.25.1":
    optional: true

  "@esbuild/linux-s390x@0.25.1":
    optional: true

  "@esbuild/linux-x64@0.25.1":
    optional: true

  "@esbuild/netbsd-arm64@0.25.1":
    optional: true

  "@esbuild/netbsd-x64@0.25.1":
    optional: true

  "@esbuild/openbsd-arm64@0.25.1":
    optional: true

  "@esbuild/openbsd-x64@0.25.1":
    optional: true

  "@esbuild/sunos-x64@0.25.1":
    optional: true

  "@esbuild/win32-arm64@0.25.1":
    optional: true

  "@esbuild/win32-ia32@0.25.1":
    optional: true

  "@esbuild/win32-x64@0.25.1":
    optional: true

  "@fastify/busboy@2.1.1": {}

  "@gar/promisify@1.1.3":
    optional: true

  "@google/genai@1.2.0(@modelcontextprotocol/sdk@1.12.1)(encoding@0.1.13)":
    dependencies:
      "@modelcontextprotocol/sdk": 1.12.1
      google-auth-library: 9.15.1(encoding@0.1.13)
      ws: 8.18.1
      zod: 3.24.2
      zod-to-json-schema: 3.24.5(zod@3.24.2)
    transitivePeerDependencies:
      - bufferutil
      - encoding
      - supports-color
      - utf-8-validate

  "@isaacs/cliui@8.0.2":
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  "@istanbuljs/load-nyc-config@1.1.0":
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  "@istanbuljs/schema@0.1.3": {}

  "@jest/console@29.7.0":
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  "@jest/core@29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))":
    dependencies:
      "@jest/console": 29.7.0
      "@jest/reporters": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0
      jest-runner: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  "@jest/environment@29.7.0":
    dependencies:
      "@jest/fake-timers": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      jest-mock: 29.7.0

  "@jest/expect-utils@29.7.0":
    dependencies:
      jest-get-type: 29.6.3

  "@jest/expect@29.7.0":
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  "@jest/fake-timers@29.7.0":
    dependencies:
      "@jest/types": 29.6.3
      "@sinonjs/fake-timers": 10.3.0
      "@types/node": 22.13.5
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  "@jest/globals@29.7.0":
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/expect": 29.7.0
      "@jest/types": 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  "@jest/reporters@29.7.0":
    dependencies:
      "@bcoe/v8-coverage": 0.2.3
      "@jest/console": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@jridgewell/trace-mapping": 0.3.25
      "@types/node": 22.13.5
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.3.0
    transitivePeerDependencies:
      - supports-color

  "@jest/schemas@29.6.3":
    dependencies:
      "@sinclair/typebox": 0.27.8

  "@jest/source-map@29.6.3":
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  "@jest/test-result@29.7.0":
    dependencies:
      "@jest/console": 29.7.0
      "@jest/types": 29.6.3
      "@types/istanbul-lib-coverage": 2.0.6
      collect-v8-coverage: 1.0.2

  "@jest/test-sequencer@29.7.0":
    dependencies:
      "@jest/test-result": 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  "@jest/transform@29.7.0":
    dependencies:
      "@babel/core": 7.26.9
      "@jest/types": 29.6.3
      "@jridgewell/trace-mapping": 0.3.25
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.8
      pirates: 4.0.6
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  "@jest/types@29.6.3":
    dependencies:
      "@jest/schemas": 29.6.3
      "@types/istanbul-lib-coverage": 2.0.6
      "@types/istanbul-reports": 3.0.4
      "@types/node": 22.13.5
      "@types/yargs": 17.0.33
      chalk: 4.1.2

  "@jridgewell/gen-mapping@0.3.8":
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.5.0
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/resolve-uri@3.1.2": {}

  "@jridgewell/set-array@1.2.1": {}

  "@jridgewell/sourcemap-codec@1.5.0": {}

  "@jridgewell/trace-mapping@0.3.25":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.5.0

  "@jridgewell/trace-mapping@0.3.9":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.5.0

  "@langchain/core@0.3.44(openai@4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2))":
    dependencies:
      "@cfworker/json-schema": 4.1.1
      ansi-styles: 5.2.0
      camelcase: 6.3.0
      decamelize: 1.2.0
      js-tiktoken: 1.0.19
      langsmith: 0.3.15(openai@4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2))
      mustache: 4.2.0
      p-queue: 6.6.2
      p-retry: 4.6.2
      uuid: 10.0.0
      zod: 3.24.2
      zod-to-json-schema: 3.24.5(zod@3.24.2)
    transitivePeerDependencies:
      - openai

  "@mistralai/mistralai@1.5.2(zod@3.24.2)":
    dependencies:
      zod: 3.24.2
      zod-to-json-schema: 3.24.5(zod@3.24.2)

  "@modelcontextprotocol/sdk@1.12.1":
    dependencies:
      ajv: 6.12.6
      content-type: 1.0.5
      cors: 2.8.5
      cross-spawn: 7.0.6
      eventsource: 3.0.7
      express: 5.1.0
      express-rate-limit: 7.5.0(express@5.1.0)
      pkce-challenge: 5.0.0
      raw-body: 3.0.0
      zod: 3.24.2
      zod-to-json-schema: 3.24.5(zod@3.24.2)
    transitivePeerDependencies:
      - supports-color

  "@nodelib/fs.scandir@2.1.5":
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      run-parallel: 1.2.0

  "@nodelib/fs.stat@2.0.5": {}

  "@nodelib/fs.walk@1.2.8":
    dependencies:
      "@nodelib/fs.scandir": 2.1.5
      fastq: 1.19.1

  "@npmcli/fs@1.1.1":
    dependencies:
      "@gar/promisify": 1.1.3
      semver: 7.7.1
    optional: true

  "@npmcli/move-file@1.1.2":
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    optional: true

  "@pkgjs/parseargs@0.11.0":
    optional: true

  "@qdrant/js-client-rest@1.13.0(typescript@5.5.4)":
    dependencies:
      "@qdrant/openapi-typescript-fetch": 1.2.6
      "@sevinf/maybe": 0.5.0
      typescript: 5.5.4
      undici: 5.28.5

  "@qdrant/openapi-typescript-fetch@1.2.6": {}

  "@redis/bloom@1.2.0(@redis/client@1.6.0)":
    dependencies:
      "@redis/client": 1.6.0

  "@redis/client@1.6.0":
    dependencies:
      cluster-key-slot: 1.1.2
      generic-pool: 3.9.0
      yallist: 4.0.0

  "@redis/graph@1.1.1(@redis/client@1.6.0)":
    dependencies:
      "@redis/client": 1.6.0

  "@redis/json@1.0.7(@redis/client@1.6.0)":
    dependencies:
      "@redis/client": 1.6.0

  "@redis/search@1.2.0(@redis/client@1.6.0)":
    dependencies:
      "@redis/client": 1.6.0

  "@redis/time-series@1.1.0(@redis/client@1.6.0)":
    dependencies:
      "@redis/client": 1.6.0

  "@rollup/rollup-android-arm-eabi@4.37.0":
    optional: true

  "@rollup/rollup-android-arm64@4.37.0":
    optional: true

  "@rollup/rollup-darwin-arm64@4.37.0":
    optional: true

  "@rollup/rollup-darwin-x64@4.37.0":
    optional: true

  "@rollup/rollup-freebsd-arm64@4.37.0":
    optional: true

  "@rollup/rollup-freebsd-x64@4.37.0":
    optional: true

  "@rollup/rollup-linux-arm-gnueabihf@4.37.0":
    optional: true

  "@rollup/rollup-linux-arm-musleabihf@4.37.0":
    optional: true

  "@rollup/rollup-linux-arm64-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-arm64-musl@4.37.0":
    optional: true

  "@rollup/rollup-linux-loongarch64-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-powerpc64le-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-riscv64-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-riscv64-musl@4.37.0":
    optional: true

  "@rollup/rollup-linux-s390x-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-x64-gnu@4.37.0":
    optional: true

  "@rollup/rollup-linux-x64-musl@4.37.0":
    optional: true

  "@rollup/rollup-win32-arm64-msvc@4.37.0":
    optional: true

  "@rollup/rollup-win32-ia32-msvc@4.37.0":
    optional: true

  "@rollup/rollup-win32-x64-msvc@4.37.0":
    optional: true

  "@sevinf/maybe@0.5.0": {}

  "@sinclair/typebox@0.27.8": {}

  "@sinonjs/commons@3.0.1":
    dependencies:
      type-detect: 4.0.8

  "@sinonjs/fake-timers@10.3.0":
    dependencies:
      "@sinonjs/commons": 3.0.1

  "@supabase/auth-js@2.68.0":
    dependencies:
      "@supabase/node-fetch": 2.6.15

  "@supabase/functions-js@2.4.4":
    dependencies:
      "@supabase/node-fetch": 2.6.15

  "@supabase/node-fetch@2.6.15":
    dependencies:
      whatwg-url: 5.0.0

  "@supabase/postgrest-js@1.19.2":
    dependencies:
      "@supabase/node-fetch": 2.6.15

  "@supabase/realtime-js@2.11.2":
    dependencies:
      "@supabase/node-fetch": 2.6.15
      "@types/phoenix": 1.6.6
      "@types/ws": 8.18.0
      ws: 8.18.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  "@supabase/storage-js@2.7.1":
    dependencies:
      "@supabase/node-fetch": 2.6.15

  "@supabase/supabase-js@2.49.1":
    dependencies:
      "@supabase/auth-js": 2.68.0
      "@supabase/functions-js": 2.4.4
      "@supabase/node-fetch": 2.6.15
      "@supabase/postgrest-js": 1.19.2
      "@supabase/realtime-js": 2.11.2
      "@supabase/storage-js": 2.7.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  "@tootallnate/once@1.1.2":
    optional: true

  "@tsconfig/node10@1.0.11": {}

  "@tsconfig/node12@1.0.11": {}

  "@tsconfig/node14@1.0.3": {}

  "@tsconfig/node16@1.0.4": {}

  "@types/babel__core@7.20.5":
    dependencies:
      "@babel/parser": 7.26.9
      "@babel/types": 7.26.9
      "@types/babel__generator": 7.6.8
      "@types/babel__template": 7.4.4
      "@types/babel__traverse": 7.20.6

  "@types/babel__generator@7.6.8":
    dependencies:
      "@babel/types": 7.26.9

  "@types/babel__template@7.4.4":
    dependencies:
      "@babel/parser": 7.26.9
      "@babel/types": 7.26.9

  "@types/babel__traverse@7.20.6":
    dependencies:
      "@babel/types": 7.26.9

  "@types/estree@1.0.6": {}

  "@types/graceful-fs@4.1.9":
    dependencies:
      "@types/node": 22.13.5

  "@types/istanbul-lib-coverage@2.0.6": {}

  "@types/istanbul-lib-report@3.0.3":
    dependencies:
      "@types/istanbul-lib-coverage": 2.0.6

  "@types/istanbul-reports@3.0.4":
    dependencies:
      "@types/istanbul-lib-report": 3.0.3

  "@types/jest@29.5.14":
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  "@types/node-fetch@2.6.12":
    dependencies:
      "@types/node": 22.13.5
      form-data: 4.0.2

  "@types/node@18.19.76":
    dependencies:
      undici-types: 5.26.5

  "@types/node@22.13.5":
    dependencies:
      undici-types: 6.20.0

  "@types/normalize-package-data@2.4.4": {}

  "@types/pg@8.11.0":
    dependencies:
      "@types/node": 22.13.5
      pg-protocol: 1.7.1
      pg-types: 4.0.2

  "@types/phoenix@1.6.6": {}

  "@types/retry@0.12.0": {}

  "@types/sqlite3@3.1.11":
    dependencies:
      "@types/node": 22.13.5

  "@types/stack-utils@2.0.3": {}

  "@types/uuid@10.0.0": {}

  "@types/uuid@9.0.8": {}

  "@types/ws@8.18.0":
    dependencies:
      "@types/node": 22.13.5

  "@types/yargs-parser@21.0.3": {}

  "@types/yargs@17.0.33":
    dependencies:
      "@types/yargs-parser": 21.0.3

  abbrev@1.1.1:
    optional: true

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  accepts@2.0.0:
    dependencies:
      mime-types: 3.0.1
      negotiator: 1.0.0

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.14.0

  acorn@8.14.0: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.0(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    optional: true

  agent-base@7.1.3: {}

  agentkeepalive@4.6.0:
    dependencies:
      humanize-ms: 1.2.1

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    optional: true

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  aproba@2.0.0:
    optional: true

  are-we-there-yet@3.0.1:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2
    optional: true

  arg@4.1.3: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  async@3.2.6: {}

  asynckit@0.4.0: {}

  axios@1.7.7:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-jest@29.7.0(@babel/core@7.26.9):
    dependencies:
      "@babel/core": 7.26.9
      "@jest/transform": 29.7.0
      "@types/babel__core": 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 29.6.3(@babel/core@7.26.9)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      "@babel/helper-plugin-utils": 7.26.5
      "@istanbuljs/load-nyc-config": 1.1.0
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      "@babel/template": 7.26.9
      "@babel/types": 7.26.9
      "@types/babel__core": 7.20.5
      "@types/babel__traverse": 7.20.6

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.26.9):
    dependencies:
      "@babel/core": 7.26.9
      "@babel/plugin-syntax-async-generators": 7.8.4(@babel/core@7.26.9)
      "@babel/plugin-syntax-bigint": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-class-properties": 7.12.13(@babel/core@7.26.9)
      "@babel/plugin-syntax-class-static-block": 7.14.5(@babel/core@7.26.9)
      "@babel/plugin-syntax-import-attributes": 7.26.0(@babel/core@7.26.9)
      "@babel/plugin-syntax-import-meta": 7.10.4(@babel/core@7.26.9)
      "@babel/plugin-syntax-json-strings": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-logical-assignment-operators": 7.10.4(@babel/core@7.26.9)
      "@babel/plugin-syntax-nullish-coalescing-operator": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-numeric-separator": 7.10.4(@babel/core@7.26.9)
      "@babel/plugin-syntax-object-rest-spread": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-optional-catch-binding": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-optional-chaining": 7.8.3(@babel/core@7.26.9)
      "@babel/plugin-syntax-private-property-in-object": 7.14.5(@babel/core@7.26.9)
      "@babel/plugin-syntax-top-level-await": 7.14.5(@babel/core@7.26.9)

  babel-preset-jest@29.6.3(@babel/core@7.26.9):
    dependencies:
      "@babel/core": 7.26.9
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.26.9)

  balanced-match@1.0.2: {}

  base-64@0.1.0: {}

  base64-js@1.5.1: {}

  bignumber.js@9.2.0: {}

  binary-extensions@2.3.0: {}

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  body-parser@2.2.0:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 4.4.0(supports-color@5.5.0)
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      on-finished: 2.4.1
      qs: 6.14.0
      raw-body: 3.0.0
      type-is: 2.0.1
    transitivePeerDependencies:
      - supports-color

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001701
      electron-to-chromium: 1.5.109
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  bs-logger@0.2.6:
    dependencies:
      fast-json-stable-stringify: 2.1.0

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-equal-constant-time@1.0.1: {}

  buffer-from@1.1.2: {}

  buffer-writer@2.0.0: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  bundle-require@5.1.0(esbuild@0.25.1):
    dependencies:
      esbuild: 0.25.1
      load-tsconfig: 0.2.5

  bytes@3.1.2: {}

  cac@6.7.14: {}

  cacache@15.3.0:
    dependencies:
      "@npmcli/fs": 1.1.1
      "@npmcli/move-file": 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    optional: true

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001701: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  charenc@0.0.2: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chownr@1.1.4: {}

  chownr@2.0.0: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.3: {}

  clean-stack@2.2.0:
    optional: true

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cluster-key-slot@1.1.2: {}

  co@4.6.0: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-support@1.1.3:
    optional: true

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@4.1.1: {}

  concat-map@0.0.1: {}

  consola@3.4.2: {}

  console-control-strings@1.1.0:
    optional: true

  console-table-printer@2.12.1:
    dependencies:
      simple-wcswidth: 1.0.1

  content-disposition@1.0.0:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.2.2: {}

  cookie@0.7.2: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  create-jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)):
    dependencies:
      "@jest/types": 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  create-require@1.1.1: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypt@0.0.2: {}

  debug@4.4.0(supports-color@5.5.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 5.5.0

  decamelize@1.2.0: {}

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  dedent@1.5.3: {}

  deep-extend@0.6.0: {}

  deepmerge@4.3.1: {}

  delayed-stream@1.0.0: {}

  delegates@1.0.0:
    optional: true

  depd@2.0.0: {}

  detect-libc@2.0.3: {}

  detect-newline@3.1.0: {}

  diff-sequences@29.6.3: {}

  diff@4.0.2: {}

  digest-fetch@1.3.0:
    dependencies:
      base-64: 0.1.0
      md5: 2.3.0

  dotenv@16.4.7: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.109: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encodeurl@2.0.0: {}

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3
    optional: true

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  env-paths@2.2.1:
    optional: true

  err-code@2.0.3:
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild@0.25.1:
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.25.1
      "@esbuild/android-arm": 0.25.1
      "@esbuild/android-arm64": 0.25.1
      "@esbuild/android-x64": 0.25.1
      "@esbuild/darwin-arm64": 0.25.1
      "@esbuild/darwin-x64": 0.25.1
      "@esbuild/freebsd-arm64": 0.25.1
      "@esbuild/freebsd-x64": 0.25.1
      "@esbuild/linux-arm": 0.25.1
      "@esbuild/linux-arm64": 0.25.1
      "@esbuild/linux-ia32": 0.25.1
      "@esbuild/linux-loong64": 0.25.1
      "@esbuild/linux-mips64el": 0.25.1
      "@esbuild/linux-ppc64": 0.25.1
      "@esbuild/linux-riscv64": 0.25.1
      "@esbuild/linux-s390x": 0.25.1
      "@esbuild/linux-x64": 0.25.1
      "@esbuild/netbsd-arm64": 0.25.1
      "@esbuild/netbsd-x64": 0.25.1
      "@esbuild/openbsd-arm64": 0.25.1
      "@esbuild/openbsd-x64": 0.25.1
      "@esbuild/sunos-x64": 0.25.1
      "@esbuild/win32-arm64": 0.25.1
      "@esbuild/win32-ia32": 0.25.1
      "@esbuild/win32-x64": 0.25.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@2.0.0: {}

  esprima@4.0.1: {}

  etag@1.8.1: {}

  event-target-shim@5.0.1: {}

  eventemitter3@4.0.7: {}

  eventsource-parser@3.0.2: {}

  eventsource@3.0.7:
    dependencies:
      eventsource-parser: 3.0.2

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expand-template@2.0.3: {}

  expect@29.7.0:
    dependencies:
      "@jest/expect-utils": 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  express-rate-limit@7.5.0(express@5.1.0):
    dependencies:
      express: 5.1.0

  express@5.1.0:
    dependencies:
      accepts: 2.0.0
      body-parser: 2.2.0
      content-disposition: 1.0.0
      content-type: 1.0.5
      cookie: 0.7.2
      cookie-signature: 1.2.2
      debug: 4.4.0(supports-color@5.5.0)
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 2.1.0
      fresh: 2.0.0
      http-errors: 2.0.0
      merge-descriptors: 2.0.0
      mime-types: 3.0.1
      on-finished: 2.4.1
      once: 1.4.0
      parseurl: 1.3.3
      proxy-addr: 2.0.7
      qs: 6.14.0
      range-parser: 1.2.1
      router: 2.2.0
      send: 1.2.0
      serve-static: 2.2.0
      statuses: 2.0.1
      type-is: 2.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend@3.0.2: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      "@nodelib/fs.walk": 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  fdir@6.4.3(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  file-uri-to-path@1.0.0: {}

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@2.1.0:
    dependencies:
      debug: 4.4.0(supports-color@5.5.0)
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  fix-tsup-cjs@1.2.0:
    dependencies:
      cac: 6.7.14
      fast-glob: 3.3.3
      kolorist: 1.8.0
      read-pkg: 8.1.0

  follow-redirects@1.15.9: {}

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data-encoder@1.7.2: {}

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  forwarded@0.2.0: {}

  fresh@2.0.0: {}

  fs-constants@1.0.0: {}

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gauge@4.0.4:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5
    optional: true

  gaxios@6.7.1(encoding@0.1.13):
    dependencies:
      extend: 3.0.2
      https-proxy-agent: 7.0.6
      is-stream: 2.0.1
      node-fetch: 2.7.0(encoding@0.1.13)
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  gcp-metadata@6.1.1(encoding@0.1.13):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)
      google-logging-utils: 0.0.2
      json-bigint: 1.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  generic-pool@3.9.0: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-package-type@0.1.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@6.0.1: {}

  github-from-package@0.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  google-auth-library@9.15.1(encoding@0.1.13):
    dependencies:
      base64-js: 1.5.1
      ecdsa-sig-formatter: 1.0.11
      gaxios: 6.7.1(encoding@0.1.13)
      gcp-metadata: 6.1.1(encoding@0.1.13)
      gtoken: 7.1.0(encoding@0.1.13)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  google-logging-utils@0.0.2: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  groq-sdk@0.3.0(encoding@0.1.13):
    dependencies:
      "@types/node": 18.19.76
      "@types/node-fetch": 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      digest-fetch: 1.3.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0(encoding@0.1.13)
      web-streams-polyfill: 3.3.3
    transitivePeerDependencies:
      - encoding

  gtoken@7.1.0(encoding@0.1.13):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  has-unicode@2.0.1:
    optional: true

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hosted-git-info@7.0.2:
    dependencies:
      lru-cache: 10.4.3

  html-escaper@2.0.2: {}

  http-cache-semantics@4.1.1:
    optional: true

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@4.0.1:
    dependencies:
      "@tootallnate/once": 1.1.2
      agent-base: 6.0.2
      debug: 4.4.0(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    optional: true

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    optional: true

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore-by-default@1.0.1: {}

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0:
    optional: true

  infer-owner@1.0.4:
    optional: true

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  ip-address@9.0.5:
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3
    optional: true

  ipaddr.js@1.9.1: {}

  is-arrayish@0.2.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-buffer@1.1.6: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-lambda@1.0.1:
    optional: true

  is-number@7.0.0: {}

  is-promise@4.0.0: {}

  is-stream@2.0.1: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      "@babel/core": 7.26.9
      "@babel/parser": 7.26.9
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.3:
    dependencies:
      "@babel/core": 7.26.9
      "@babel/parser": 7.26.9
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.0(supports-color@5.5.0)
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.3:
    dependencies:
      "@isaacs/cliui": 8.0.2
    optionalDependencies:
      "@pkgjs/parseargs": 0.11.0

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/expect": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.5.3
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)):
    dependencies:
      "@jest/core": 29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      exit: 0.1.2
      import-local: 3.2.0
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)):
    dependencies:
      "@babel/core": 7.26.9
      "@jest/test-sequencer": 29.7.0
      "@jest/types": 29.6.3
      babel-jest: 29.7.0(@babel/core@7.26.9)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      "@types/node": 22.13.5
      ts-node: 10.9.2(@types/node@22.13.5)(typescript@5.5.4)
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-node@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/fake-timers": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/graceful-fs": 4.1.9
      "@types/node": 22.13.5
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      "@babel/code-frame": 7.26.2
      "@jest/types": 29.6.3
      "@types/stack-utils": 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0:
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.10
      resolve.exports: 2.0.3
      slash: 3.0.0

  jest-runner@29.7.0:
    dependencies:
      "@jest/console": 29.7.0
      "@jest/environment": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/fake-timers": 29.7.0
      "@jest/globals": 29.7.0
      "@jest/source-map": 29.6.3
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      chalk: 4.1.2
      cjs-module-lexer: 1.4.3
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0:
    dependencies:
      "@babel/core": 7.26.9
      "@babel/generator": 7.26.9
      "@babel/plugin-syntax-jsx": 7.25.9(@babel/core@7.26.9)
      "@babel/plugin-syntax-typescript": 7.25.9(@babel/core@7.26.9)
      "@babel/types": 7.26.9
      "@jest/expect-utils": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.26.9)
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watcher@29.7.0:
    dependencies:
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 22.13.5
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@29.7.0:
    dependencies:
      "@types/node": 22.13.5
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)):
    dependencies:
      "@jest/core": 29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      "@jest/types": 29.6.3
      import-local: 3.2.0
      jest-cli: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  joycon@3.1.1: {}

  js-tiktoken@1.0.19:
    dependencies:
      base64-js: 1.5.1

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  jsbn@1.1.0:
    optional: true

  jsesc@3.1.0: {}

  json-bigint@1.0.0:
    dependencies:
      bignumber.js: 9.2.0

  json-parse-even-better-errors@2.3.1: {}

  json-parse-even-better-errors@3.0.2: {}

  json-schema-traverse@0.4.1: {}

  json5@2.2.3: {}

  jwa@2.0.0:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@4.0.0:
    dependencies:
      jwa: 2.0.0
      safe-buffer: 5.2.1

  kleur@3.0.3: {}

  kolorist@1.8.0: {}

  langsmith@0.3.15(openai@4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2)):
    dependencies:
      "@types/uuid": 10.0.0
      chalk: 4.1.2
      console-table-printer: 2.12.1
      p-queue: 6.6.2
      p-retry: 4.6.2
      semver: 7.7.1
      uuid: 10.0.0
    optionalDependencies:
      openai: 4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2)

  leven@3.1.0: {}

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lines-and-columns@2.0.4: {}

  load-tsconfig@0.2.5: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash.memoize@4.1.2: {}

  lodash.sortby@4.7.0: {}

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0
    optional: true

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.1

  make-error@1.3.6: {}

  make-fetch-happen@9.1.0:
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 15.3.0
      http-cache-semantics: 4.1.1
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 1.4.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      promise-retry: 2.0.1
      socks-proxy-agent: 6.2.1
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    optional: true

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  math-intrinsics@1.1.0: {}

  md5@2.3.0:
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6

  media-typer@1.1.0: {}

  merge-descriptors@2.0.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime-types@3.0.1:
    dependencies:
      mime-db: 1.54.0

  mimic-fn@2.1.0: {}

  mimic-response@3.1.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass-collect@1.0.2:
    dependencies:
      minipass: 3.3.6
    optional: true

  minipass-fetch@1.4.1:
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13
    optional: true

  minipass-flush@1.0.5:
    dependencies:
      minipass: 3.3.6
    optional: true

  minipass-pipeline@1.2.4:
    dependencies:
      minipass: 3.3.6
    optional: true

  minipass-sized@1.0.3:
    dependencies:
      minipass: 3.3.6
    optional: true

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp-classic@0.5.3: {}

  mkdirp@1.0.4: {}

  ms@2.1.3: {}

  mustache@4.2.0: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.8:
    optional: true

  napi-build-utils@2.0.0: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.4:
    optional: true

  negotiator@1.0.0: {}

  neo4j-driver-bolt-connection@5.28.1:
    dependencies:
      buffer: 6.0.3
      neo4j-driver-core: 5.28.1
      string_decoder: 1.3.0

  neo4j-driver-core@5.28.1: {}

  neo4j-driver@5.28.1:
    dependencies:
      neo4j-driver-bolt-connection: 5.28.1
      neo4j-driver-core: 5.28.1
      rxjs: 7.8.2

  node-abi@3.74.0:
    dependencies:
      semver: 7.7.1

  node-addon-api@7.1.1: {}

  node-domexception@1.0.0: {}

  node-fetch@2.7.0(encoding@0.1.13):
    dependencies:
      whatwg-url: 5.0.0
    optionalDependencies:
      encoding: 0.1.13

  node-gyp@8.4.1:
    dependencies:
      env-paths: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      make-fetch-happen: 9.1.0
      nopt: 5.0.0
      npmlog: 6.0.2
      rimraf: 3.0.2
      semver: 7.7.1
      tar: 6.2.1
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
    optional: true

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  nodemon@3.1.9:
    dependencies:
      chokidar: 3.6.0
      debug: 4.4.0(supports-color@5.5.0)
      ignore-by-default: 1.0.1
      minimatch: 3.1.2
      pstree.remy: 1.1.8
      semver: 7.7.1
      simple-update-notifier: 2.0.0
      supports-color: 5.5.0
      touch: 3.1.1
      undefsafe: 2.0.5

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1
    optional: true

  normalize-package-data@6.0.2:
    dependencies:
      hosted-git-info: 7.0.2
      semver: 7.7.1
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npmlog@6.0.2:
    dependencies:
      are-we-there-yet: 3.0.1
      console-control-strings: 1.1.0
      gauge: 4.0.4
      set-blocking: 2.0.0
    optional: true

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  obuf@1.1.2: {}

  ollama@0.5.14:
    dependencies:
      whatwg-fetch: 3.6.20

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  openai@4.93.0(encoding@0.1.13)(ws@8.18.1)(zod@3.24.2):
    dependencies:
      "@types/node": 18.19.76
      "@types/node-fetch": 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0(encoding@0.1.13)
    optionalDependencies:
      ws: 8.18.1
      zod: 3.24.2
    transitivePeerDependencies:
      - encoding

  p-finally@1.0.0: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0
    optional: true

  p-queue@6.6.2:
    dependencies:
      eventemitter3: 4.0.7
      p-timeout: 3.2.0

  p-retry@4.6.2:
    dependencies:
      "@types/retry": 0.12.0
      retry: 0.13.1

  p-timeout@3.2.0:
    dependencies:
      p-finally: 1.0.0

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  packet-reader@1.0.0: {}

  parse-json@5.2.0:
    dependencies:
      "@babel/code-frame": 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-json@7.1.1:
    dependencies:
      "@babel/code-frame": 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 3.0.2
      lines-and-columns: 2.0.4
      type-fest: 3.13.1

  parseurl@1.3.3: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-to-regexp@8.2.0: {}

  pg-cloudflare@1.1.1:
    optional: true

  pg-connection-string@2.7.0: {}

  pg-int8@1.0.1: {}

  pg-numeric@1.0.2: {}

  pg-pool@3.7.1(pg@8.11.3):
    dependencies:
      pg: 8.11.3

  pg-protocol@1.7.1: {}

  pg-types@2.2.0:
    dependencies:
      pg-int8: 1.0.1
      postgres-array: 2.0.0
      postgres-bytea: 1.0.0
      postgres-date: 1.0.7
      postgres-interval: 1.2.0

  pg-types@4.0.2:
    dependencies:
      pg-int8: 1.0.1
      pg-numeric: 1.0.2
      postgres-array: 3.0.2
      postgres-bytea: 3.0.0
      postgres-date: 2.1.0
      postgres-interval: 3.0.0
      postgres-range: 1.1.4

  pg@8.11.3:
    dependencies:
      buffer-writer: 2.0.0
      packet-reader: 1.0.0
      pg-connection-string: 2.7.0
      pg-pool: 3.7.1(pg@8.11.3)
      pg-protocol: 1.7.1
      pg-types: 2.2.0
      pgpass: 1.0.5
    optionalDependencies:
      pg-cloudflare: 1.1.1

  pgpass@1.0.5:
    dependencies:
      split2: 4.2.0

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pirates@4.0.6: {}

  pkce-challenge@5.0.0: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  postcss-load-config@6.0.1(postcss@8.5.3):
    dependencies:
      lilconfig: 3.1.3
    optionalDependencies:
      postcss: 8.5.3

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1
    optional: true

  postgres-array@2.0.0: {}

  postgres-array@3.0.2: {}

  postgres-bytea@1.0.0: {}

  postgres-bytea@3.0.0:
    dependencies:
      obuf: 1.1.2

  postgres-date@1.0.7: {}

  postgres-date@2.1.0: {}

  postgres-interval@1.2.0:
    dependencies:
      xtend: 4.0.2

  postgres-interval@3.0.0: {}

  postgres-range@1.1.4: {}

  prebuild-install@7.1.3:
    dependencies:
      detect-libc: 2.0.3
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 2.0.0
      node-abi: 3.74.0
      pump: 3.0.2
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.1.2
      tunnel-agent: 0.6.0

  prettier@3.5.2: {}

  pretty-format@29.7.0:
    dependencies:
      "@jest/schemas": 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  promise-inflight@1.0.1:
    optional: true

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0
    optional: true

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  pstree.remy@1.1.8: {}

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  queue-microtask@1.2.3: {}

  range-parser@1.2.1: {}

  raw-body@3.0.0:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      unpipe: 1.0.0

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-is@18.3.1: {}

  read-pkg@8.1.0:
    dependencies:
      "@types/normalize-package-data": 2.4.4
      normalize-package-data: 6.0.2
      parse-json: 7.1.1
      type-fest: 4.35.0

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  redis@4.7.0:
    dependencies:
      "@redis/bloom": 1.2.0(@redis/client@1.6.0)
      "@redis/client": 1.6.0
      "@redis/graph": 1.1.1(@redis/client@1.6.0)
      "@redis/json": 1.0.7(@redis/client@1.6.0)
      "@redis/search": 1.2.0(@redis/client@1.6.0)
      "@redis/time-series": 1.1.0(@redis/client@1.6.0)

  require-directory@2.1.1: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@5.0.0: {}

  resolve.exports@2.0.3: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  retry@0.12.0:
    optional: true

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3
    optional: true

  rimraf@5.0.10:
    dependencies:
      glob: 10.4.5

  rollup@4.37.0:
    dependencies:
      "@types/estree": 1.0.6
    optionalDependencies:
      "@rollup/rollup-android-arm-eabi": 4.37.0
      "@rollup/rollup-android-arm64": 4.37.0
      "@rollup/rollup-darwin-arm64": 4.37.0
      "@rollup/rollup-darwin-x64": 4.37.0
      "@rollup/rollup-freebsd-arm64": 4.37.0
      "@rollup/rollup-freebsd-x64": 4.37.0
      "@rollup/rollup-linux-arm-gnueabihf": 4.37.0
      "@rollup/rollup-linux-arm-musleabihf": 4.37.0
      "@rollup/rollup-linux-arm64-gnu": 4.37.0
      "@rollup/rollup-linux-arm64-musl": 4.37.0
      "@rollup/rollup-linux-loongarch64-gnu": 4.37.0
      "@rollup/rollup-linux-powerpc64le-gnu": 4.37.0
      "@rollup/rollup-linux-riscv64-gnu": 4.37.0
      "@rollup/rollup-linux-riscv64-musl": 4.37.0
      "@rollup/rollup-linux-s390x-gnu": 4.37.0
      "@rollup/rollup-linux-x64-gnu": 4.37.0
      "@rollup/rollup-linux-x64-musl": 4.37.0
      "@rollup/rollup-win32-arm64-msvc": 4.37.0
      "@rollup/rollup-win32-ia32-msvc": 4.37.0
      "@rollup/rollup-win32-x64-msvc": 4.37.0
      fsevents: 2.3.3

  router@2.2.0:
    dependencies:
      debug: 4.4.0(supports-color@5.5.0)
      depd: 2.0.0
      is-promise: 4.0.0
      parseurl: 1.3.3
      path-to-regexp: 8.2.0
    transitivePeerDependencies:
      - supports-color

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  semver@6.3.1: {}

  semver@7.7.1: {}

  send@1.2.0:
    dependencies:
      debug: 4.4.0(supports-color@5.5.0)
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 2.0.0
      http-errors: 2.0.0
      mime-types: 3.0.1
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serve-static@2.2.0:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 1.2.0
    transitivePeerDependencies:
      - supports-color

  set-blocking@2.0.0:
    optional: true

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1: {}

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1

  simple-update-notifier@2.0.0:
    dependencies:
      semver: 7.7.1

  simple-wcswidth@1.0.1: {}

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  smart-buffer@4.2.0:
    optional: true

  socks-proxy-agent@6.2.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0(supports-color@5.5.0)
      socks: 2.8.4
    transitivePeerDependencies:
      - supports-color
    optional: true

  socks@2.8.4:
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0
    optional: true

  source-map-js@1.2.1:
    optional: true

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.8.0-beta.0:
    dependencies:
      whatwg-url: 7.1.0

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.21

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21

  spdx-license-ids@3.0.21: {}

  split2@4.2.0: {}

  sprintf-js@1.0.3: {}

  sprintf-js@1.1.3:
    optional: true

  sqlite3@5.1.7:
    dependencies:
      bindings: 1.5.0
      node-addon-api: 7.1.1
      prebuild-install: 7.1.3
      tar: 6.2.1
    optionalDependencies:
      node-gyp: 8.4.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  ssri@8.0.1:
    dependencies:
      minipass: 3.3.6
    optional: true

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  statuses@2.0.1: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@2.0.1: {}

  strip-json-comments@3.1.1: {}

  sucrase@3.35.0:
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  tar-fs@2.1.2:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.2
      tar-stream: 2.2.0

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  test-exclude@6.0.0:
    dependencies:
      "@istanbuljs/schema": 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  tinyexec@0.3.2: {}

  tinyglobby@0.2.12:
    dependencies:
      fdir: 6.4.3(picomatch@4.0.2)
      picomatch: 4.0.2

  tmpl@1.0.5: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  touch@3.1.1: {}

  tr46@0.0.3: {}

  tr46@1.0.1:
    dependencies:
      punycode: 2.3.1

  tree-kill@1.2.2: {}

  ts-interface-checker@0.1.13: {}

  ts-jest@29.2.6(@babel/core@7.26.9)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.26.9))(esbuild@0.25.1)(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4)))(typescript@5.5.4):
    dependencies:
      bs-logger: 0.2.6
      ejs: 3.1.10
      fast-json-stable-stringify: 2.1.0
      jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4))
      jest-util: 29.7.0
      json5: 2.2.3
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.7.1
      typescript: 5.5.4
      yargs-parser: 21.1.1
    optionalDependencies:
      "@babel/core": 7.26.9
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      babel-jest: 29.7.0(@babel/core@7.26.9)
      esbuild: 0.25.1

  ts-node@10.9.2(@types/node@22.13.5)(typescript@5.5.4):
    dependencies:
      "@cspotcode/source-map-support": 0.8.1
      "@tsconfig/node10": 1.0.11
      "@tsconfig/node12": 1.0.11
      "@tsconfig/node14": 1.0.3
      "@tsconfig/node16": 1.0.4
      "@types/node": 22.13.5
      acorn: 8.14.0
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.5.4
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tslib@2.8.1: {}

  tsup@8.4.0(postcss@8.5.3)(typescript@5.5.4):
    dependencies:
      bundle-require: 5.1.0(esbuild@0.25.1)
      cac: 6.7.14
      chokidar: 4.0.3
      consola: 3.4.2
      debug: 4.4.0(supports-color@5.5.0)
      esbuild: 0.25.1
      joycon: 3.1.1
      picocolors: 1.1.1
      postcss-load-config: 6.0.1(postcss@8.5.3)
      resolve-from: 5.0.0
      rollup: 4.37.0
      source-map: 0.8.0-beta.0
      sucrase: 3.35.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.12
      tree-kill: 1.2.2
    optionalDependencies:
      postcss: 8.5.3
      typescript: 5.5.4
    transitivePeerDependencies:
      - jiti
      - supports-color
      - tsx
      - yaml

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  type-detect@4.0.8: {}

  type-fest@0.21.3: {}

  type-fest@3.13.1: {}

  type-fest@4.35.0: {}

  type-is@2.0.1:
    dependencies:
      content-type: 1.0.5
      media-typer: 1.1.0
      mime-types: 3.0.1

  typescript@5.5.4: {}

  undefsafe@2.0.5: {}

  undici-types@5.26.5: {}

  undici-types@6.20.0: {}

  undici@5.28.5:
    dependencies:
      "@fastify/busboy": 2.1.1

  unique-filename@1.1.1:
    dependencies:
      unique-slug: 2.0.2
    optional: true

  unique-slug@2.0.2:
    dependencies:
      imurmurhash: 0.1.4
    optional: true

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  uuid@10.0.0: {}

  uuid@9.0.1: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-to-istanbul@9.3.0:
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      "@types/istanbul-lib-coverage": 2.0.6
      convert-source-map: 2.0.0

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vary@1.1.2: {}

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  web-streams-polyfill@3.3.3: {}

  web-streams-polyfill@4.0.0-beta.3: {}

  webidl-conversions@3.0.1: {}

  webidl-conversions@4.0.2: {}

  whatwg-fetch@3.6.20: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  whatwg-url@7.1.0:
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3
    optional: true

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  ws@8.18.1: {}

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}

  zod-to-json-schema@3.24.5(zod@3.24.2):
    dependencies:
      zod: 3.24.2

  zod@3.24.2: {}
