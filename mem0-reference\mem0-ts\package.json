{"name": "mem0ai", "version": "2.1.29", "description": "The Memory Layer For Your AI Apps", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/index.d.ts"], "oss": ["./dist/oss/index.d.ts"]}}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./oss": {"types": "./dist/oss/index.d.ts", "require": "./dist/oss/index.js", "import": "./dist/oss/index.mjs"}}, "files": ["dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && npx prettier --check . && npx tsup", "dev": "npx nodemon", "start": "pnpm run example memory", "example": "ts-node src/oss/examples/vector-stores/index.ts", "test": "jest", "test:ts": "jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "format": "npm run clean && prettier --write .", "format:check": "npm run clean && prettier --check ."}, "tsup": {"entry": ["src/index.ts"], "format": ["cjs", "esm"], "dts": {"resolve": true}, "splitting": false, "sourcemap": true, "clean": true, "treeshake": true, "minify": false, "external": ["@mem0/community"], "noExternal": ["!src/community/**"]}, "keywords": ["mem0", "api", "client", "memory", "llm", "long-term-memory", "ai"], "author": "<PERSON><PERSON><PERSON>", "license": "Apache-2.0", "devDependencies": {"@types/node": "^22.7.6", "@types/uuid": "^9.0.8", "dotenv": "^16.4.5", "fix-tsup-cjs": "^1.2.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.5.2", "rimraf": "^5.0.5", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "tsup": "^8.3.0", "typescript": "5.5.4"}, "dependencies": {"axios": "1.7.7", "openai": "^4.93.0", "uuid": "9.0.1", "zod": "^3.24.1"}, "peerDependencies": {"@anthropic-ai/sdk": "^0.40.1", "@google/genai": "^1.2.0", "@mistralai/mistralai": "^1.5.2", "@qdrant/js-client-rest": "1.13.0", "@supabase/supabase-js": "^2.49.1", "@types/jest": "29.5.14", "@types/pg": "8.11.0", "@types/sqlite3": "3.1.11", "groq-sdk": "0.3.0", "@langchain/core": "^0.3.44", "neo4j-driver": "^5.28.1", "ollama": "^0.5.14", "pg": "8.11.3", "redis": "^4.6.13", "sqlite3": "5.1.7"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}